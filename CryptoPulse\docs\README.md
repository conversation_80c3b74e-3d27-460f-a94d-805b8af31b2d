# CryptoTracker Documentation

Welcome to the CryptoTracker documentation repository. This directory contains comprehensive documentation for the CryptoTracker application, a cryptocurrency analysis and portfolio management platform built with Streamlit.

## Documentation Contents

### For Product Managers & Stakeholders
- [Product Requirements Document](product_requirements.md): Detailed overview of the product features, target audience, and technical requirements.

### For Developers
- [Developer Guide](developer_guide.md): Technical overview, setup instructions, and development guidelines.
- [API Documentation](api_documentation.md): Details on the external APIs used and integration patterns.
- [Architecture Overview](architecture_overview.md): System architecture, data flow, and component interactions.

## CryptoTracker Overview

CryptoTracker is a comprehensive cryptocurrency analysis and portfolio management platform that offers:

- **Real-time Cryptocurrency Tracking**: Monitor prices for Bitcoin, Ethereum, XRP, and Tether.
- **Market Overview**: Interactive charts and market metrics.
- **Portfolio Management**: Track investments, record transactions, and analyze performance.
- **Price Alerts**: Create and manage custom price alerts.
- **AI-Powered Insights**: Price predictions and technical analysis.

## Key Technologies

- **Streamlit**: Interactive UI framework
- **Plotly**: Data visualization
- **Pandas & NumPy**: Data processing
- **scikit-learn**: Machine learning for price predictions
- **CoinGecko API**: Cryptocurrency data source

## Getting Started

For new developers:

1. Review the [Developer Guide](developer_guide.md) for setup instructions
2. Understand the [Architecture Overview](architecture_overview.md) for system design
3. Reference the [API Documentation](api_documentation.md) for external integrations

## Contributing

When contributing to the project:

1. Follow the structure outlined in the architecture document
2. Adhere to the established coding patterns
3. Update documentation for any new features or changes

## Future Development

See the [Product Requirements Document](product_requirements.md) for planned future enhancements and potential new features.