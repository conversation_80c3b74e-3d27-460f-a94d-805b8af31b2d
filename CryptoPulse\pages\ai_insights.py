import streamlit as st
import plotly.graph_objects as go
from datetime import datetime, timedelta

from crypto_data import get_crypto_data
from analysis import predict_price_trend, generate_technical_analysis
from utils import get_crypto_icon

# Page title
st.title("AI Insights & Predictions")

# Cryptocurrency selector
selected_crypto = st.selectbox(
    "Select Cryptocurrency",
    ["Bitcoin", "Ethereum", "XRP", "Tether"]
)

# Map cryptocurrency names to IDs
crypto_mapping = {
    'Bitcoin': 'bitcoin',
    'Ethereum': 'ethereum',
    'XRP': 'ripple',
    'Tether': 'tether'
}

selected_crypto_id = crypto_mapping.get(selected_crypto)

# Create tabs for different analysis views
analysis_tabs = st.tabs(["Price Predictions", "Technical Analysis", "Sentiment Analysis"])

with analysis_tabs[0]:  # Price Predictions
    st.subheader(f"{selected_crypto} Price Prediction")
    
    # Prediction period selector
    prediction_days = st.slider(
        "Prediction Period (Days)",
        min_value=3,
        max_value=14,
        value=7,
        step=1
    )
    
    st.info("The prediction is based on historical price patterns and should not be considered financial advice.")
    
    # Generate price prediction
    try:
        with st.spinner(f"Generating prediction for {selected_crypto}..."):
            prediction_data, confidence = predict_price_trend(selected_crypto_id, days=prediction_days)
            
            if prediction_data is not None:
                # Split data into historical and predicted
                historical_data = prediction_data[prediction_data['predicted'] == False]
                future_data = prediction_data[prediction_data['predicted'] == True]
                
                # Create visualization
                fig = go.Figure()
                
                # Historical price line
                fig.add_trace(
                    go.Scatter(
                        x=historical_data['timestamp'],
                        y=historical_data['price'],
                        mode='lines',
                        name='Historical Price',
                        line=dict(color='#1F77B4', width=2)
                    )
                )
                
                # Predicted price line
                fig.add_trace(
                    go.Scatter(
                        x=future_data['timestamp'],
                        y=future_data['price'],
                        mode='lines',
                        name='Predicted Price',
                        line=dict(color='#FF7F0E', width=2, dash='dash')
                    )
                )
                
                # Add current price point
                current_price = historical_data.iloc[-1]['price']
                fig.add_trace(
                    go.Scatter(
                        x=[historical_data.iloc[-1]['timestamp']],
                        y=[current_price],
                        mode='markers',
                        name='Current Price',
                        marker=dict(color='#2CA02C', size=10)
                    )
                )
                
                # Layout
                fig.update_layout(
                    title=f'{selected_crypto} Price Prediction (Next {prediction_days} Days)',
                    xaxis_title='Date',
                    yaxis_title='Price (USD)',
                    hovermode='x unified',
                    template='plotly_white',
                    height=500
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # Display prediction metrics
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    last_predicted_price = future_data.iloc[-1]['price']
                    price_change = ((last_predicted_price - current_price) / current_price) * 100
                    direction = "increase" if price_change >= 0 else "decrease"
                    
                    st.metric(
                        f"Predicted Price ({prediction_days} days)",
                        f"${last_predicted_price:.2f}",
                        f"{price_change:.2f}%"
                    )
                
                with col2:
                    pred_min = future_data['price'].min()
                    pred_max = future_data['price'].max()
                    
                    st.metric(
                        "Predicted Range",
                        f"${pred_min:.2f} - ${pred_max:.2f}"
                    )
                
                with col3:
                    st.metric(
                        "Prediction Confidence",
                        f"{confidence * 100:.2f}%"
                    )
                
                # Prediction summary
                st.subheader("Prediction Summary")
                
                prediction_strength = "weak"
                if confidence > 0.7:
                    prediction_strength = "strong"
                elif confidence > 0.5:
                    prediction_strength = "moderate"
                
                st.write(f"""
                Based on historical price patterns, our model predicts that {selected_crypto} will 
                likely {direction} by approximately {abs(price_change):.2f}% over the next {prediction_days} days,
                reaching around ${last_predicted_price:.2f}. This prediction has a {prediction_strength} confidence level
                of {confidence * 100:.2f}%.
                
                Remember that cryptocurrency markets are highly volatile and predictions should be taken as one of many
                inputs for your investment decisions.
                """)
                
                # Disclaimer
                st.warning("""
                **Disclaimer**: This prediction is generated using a basic linear regression model and is for
                informational purposes only. It should not be considered as financial advice. Always do your own
                research before making investment decisions.
                """)
                
            else:
                st.error(f"Unable to generate prediction for {selected_crypto}. Please try again later.")
                st.image("https://images.unsplash.com/photo-1639768939489-025b90ba9f23", use_container_width=True)
                
    except Exception as e:
        st.error(f"Error generating prediction: {str(e)}")
        st.image("https://images.unsplash.com/photo-1639768939489-025b90ba9f23", use_container_width=True)

with analysis_tabs[1]:  # Technical Analysis
    st.subheader(f"{selected_crypto} Technical Analysis")
    
    # Time period selector
    time_period = st.radio(
        "Analysis Period:",
        ["7 Days", "14 Days", "30 Days", "90 Days"],
        horizontal=True
    )
    
    # Map time period to days
    time_mapping = {
        "7 Days": 7,
        "14 Days": 14,
        "30 Days": 30,
        "90 Days": 90
    }
    
    analysis_days = time_mapping[time_period]
    
    # Generate technical analysis
    try:
        with st.spinner(f"Generating technical analysis for {selected_crypto}..."):
            analysis_data, summary = generate_technical_analysis(selected_crypto_id, days=analysis_days)
            
            if analysis_data is not None:
                # Create tabs for different indicators
                indicator_tabs = st.tabs(["Price & Moving Averages", "RSI", "MACD", "Bollinger Bands"])
                
                with indicator_tabs[0]:  # Price & Moving Averages
                    st.subheader("Price and Moving Averages")
                    
                    fig = go.Figure()
                    
                    # Price line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['price'],
                            mode='lines',
                            name='Price',
                            line=dict(color='#1F77B4', width=2)
                        )
                    )
                    
                    # SMA line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['SMA_20'],
                            mode='lines',
                            name='20-day SMA',
                            line=dict(color='#FF7F0E', width=1.5)
                        )
                    )
                    
                    # EMA lines
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['EMA_12'],
                            mode='lines',
                            name='12-day EMA',
                            line=dict(color='#2CA02C', width=1.5)
                        )
                    )
                    
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['EMA_26'],
                            mode='lines',
                            name='26-day EMA',
                            line=dict(color='#D62728', width=1.5)
                        )
                    )
                    
                    fig.update_layout(
                        title=f'{selected_crypto} Price and Moving Averages',
                        xaxis_title='Date',
                        yaxis_title='Price (USD)',
                        hovermode='x unified',
                        template='plotly_white',
                        height=500
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                
                with indicator_tabs[1]:  # RSI
                    st.subheader("Relative Strength Index (RSI)")
                    
                    fig = go.Figure()
                    
                    # RSI line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['RSI'],
                            mode='lines',
                            name='RSI',
                            line=dict(color='#1F77B4', width=2)
                        )
                    )
                    
                    # Add overbought and oversold lines
                    fig.add_shape(
                        type='line',
                        x0=analysis_data.index[0],
                        x1=analysis_data.index[-1],
                        y0=70,
                        y1=70,
                        line=dict(color='red', width=1, dash='dash')
                    )
                    
                    fig.add_shape(
                        type='line',
                        x0=analysis_data.index[0],
                        x1=analysis_data.index[-1],
                        y0=30,
                        y1=30,
                        line=dict(color='green', width=1, dash='dash')
                    )
                    
                    # Add annotations
                    fig.add_annotation(
                        x=analysis_data.index[-1],
                        y=70,
                        text="Overbought",
                        showarrow=False,
                        yshift=10
                    )
                    
                    fig.add_annotation(
                        x=analysis_data.index[-1],
                        y=30,
                        text="Oversold",
                        showarrow=False,
                        yshift=-10
                    )
                    
                    fig.update_layout(
                        title=f'{selected_crypto} RSI (14-period)',
                        xaxis_title='Date',
                        yaxis_title='RSI',
                        yaxis=dict(range=[0, 100]),
                        hovermode='x unified',
                        template='plotly_white',
                        height=400
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # RSI explanation
                    st.markdown("""
                    **Relative Strength Index (RSI)** is a momentum oscillator that measures the speed and change of price movements.
                    
                    - RSI above 70 is considered overbought (potential selling opportunity)
                    - RSI below 30 is considered oversold (potential buying opportunity)
                    - RSI between 30-70 is considered neutral
                    """)
                
                with indicator_tabs[2]:  # MACD
                    st.subheader("Moving Average Convergence Divergence (MACD)")
                    
                    fig = go.Figure()
                    
                    # MACD line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['MACD'],
                            mode='lines',
                            name='MACD Line',
                            line=dict(color='#1F77B4', width=2)
                        )
                    )
                    
                    # Signal line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['MACD_Signal'],
                            mode='lines',
                            name='Signal Line',
                            line=dict(color='#FF7F0E', width=1.5)
                        )
                    )
                    
                    # MACD histogram
                    fig.add_trace(
                        go.Bar(
                            x=analysis_data.index,
                            y=analysis_data['MACD_Histogram'],
                            name='Histogram',
                            marker_color=analysis_data['MACD_Histogram'].apply(
                                lambda x: 'green' if x >= 0 else 'red'
                            )
                        )
                    )
                    
                    fig.update_layout(
                        title=f'{selected_crypto} MACD (12,26,9)',
                        xaxis_title='Date',
                        yaxis_title='MACD',
                        hovermode='x unified',
                        template='plotly_white',
                        height=400
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # MACD explanation
                    st.markdown("""
                    **Moving Average Convergence Divergence (MACD)** is a trend-following momentum indicator that shows the relationship between two moving averages of a security's price.
                    
                    - MACD crossing above signal line is bullish (potential buying opportunity)
                    - MACD crossing below signal line is bearish (potential selling opportunity)
                    - Histogram shows the difference between MACD and signal line (momentum)
                    """)
                
                with indicator_tabs[3]:  # Bollinger Bands
                    st.subheader("Bollinger Bands")
                    
                    fig = go.Figure()
                    
                    # Price line
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['price'],
                            mode='lines',
                            name='Price',
                            line=dict(color='#1F77B4', width=2)
                        )
                    )
                    
                    # Middle band (20-day SMA)
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['BB_Middle'],
                            mode='lines',
                            name='Middle Band (20-day SMA)',
                            line=dict(color='#FF7F0E', width=1.5)
                        )
                    )
                    
                    # Upper band
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['BB_Upper'],
                            mode='lines',
                            name='Upper Band (+2σ)',
                            line=dict(color='#2CA02C', width=1.5, dash='dash')
                        )
                    )
                    
                    # Lower band
                    fig.add_trace(
                        go.Scatter(
                            x=analysis_data.index,
                            y=analysis_data['BB_Lower'],
                            mode='lines',
                            name='Lower Band (-2σ)',
                            line=dict(color='#D62728', width=1.5, dash='dash'),
                            fill='tonexty',
                            fillcolor='rgba(231, 234, 241, 0.5)'
                        )
                    )
                    
                    fig.update_layout(
                        title=f'{selected_crypto} Bollinger Bands (20,2)',
                        xaxis_title='Date',
                        yaxis_title='Price (USD)',
                        hovermode='x unified',
                        template='plotly_white',
                        height=500
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # Bollinger Bands explanation
                    st.markdown("""
                    **Bollinger Bands** are a volatility indicator that consists of a middle band (simple moving average) with upper and lower bands based on standard deviations.
                    
                    - Price touching the upper band indicates potential overbought conditions
                    - Price touching the lower band indicates potential oversold conditions
                    - Bands widening indicate increasing volatility
                    - Bands narrowing indicate decreasing volatility
                    """)
                
                # Analysis summary
                st.subheader("Technical Analysis Summary")
                st.info(summary)
                
            else:
                st.error(f"Unable to generate technical analysis for {selected_crypto}. Please try again later.")
                st.image("https://images.unsplash.com/photo-1542744173-05336fcc7ad4", use_container_width=True)
                
    except Exception as e:
        st.error(f"Error generating technical analysis: {str(e)}")
        st.image("https://images.unsplash.com/photo-1542744173-05336fcc7ad4", use_container_width=True)

with analysis_tabs[2]:  # Sentiment Analysis
    st.subheader("Sentiment Analysis")
    st.info("Sentiment analysis is currently under development. This feature will analyze social media, news, and market sentiment for cryptocurrencies.")
    
    # Placeholder for sentiment analysis
    st.image("https://images.unsplash.com/photo-1556155092-490a1ba16284", use_container_width=True)
    
    st.markdown(f"""
    ### Future Sentiment Analysis Features for {selected_crypto}
    
    - Social media sentiment tracking (Twitter, Reddit, etc.)
    - News sentiment analysis
    - Community engagement metrics
    - Market sentiment indicators
    - Correlation with price movements
    """)
    
    st.warning("This feature will be available in a future update.")
