import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import plotly.graph_objects as go

from crypto_data import get_crypto_data

def calculate_sma(data, window):
    """
    Calculate Simple Moving Average.
    
    Args:
        data (pd.DataFrame): Price data.
        window (int): Window size for SMA.
        
    Returns:
        pd.Series: SMA values.
    """
    return data['price'].rolling(window=window).mean()

def calculate_ema(data, window):
    """
    Calculate Exponential Moving Average.
    
    Args:
        data (pd.DataFrame): Price data.
        window (int): Window size for EMA.
        
    Returns:
        pd.Series: EMA values.
    """
    return data['price'].ewm(span=window, adjust=False).mean()

def calculate_rsi(data, window=14):
    """
    Calculate Relative Strength Index.
    
    Args:
        data (pd.DataFrame): Price data.
        window (int): Window size for RSI.
        
    Returns:
        pd.Series: RSI values.
    """
    # Calculate price changes
    delta = data['price'].diff()
    
    # Separate gains and losses
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # Calculate average gain and loss
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    # Calculate RS
    rs = avg_gain / avg_loss
    
    # Calculate RSI
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def calculate_macd(data, fast=12, slow=26, signal=9):
    """
    Calculate Moving Average Convergence Divergence.
    
    Args:
        data (pd.DataFrame): Price data.
        fast (int): Fast EMA period.
        slow (int): Slow EMA period.
        signal (int): Signal EMA period.
        
    Returns:
        tuple: (MACD line, Signal line, Histogram).
    """
    # Calculate EMAs
    ema_fast = data['price'].ewm(span=fast, adjust=False).mean()
    ema_slow = data['price'].ewm(span=slow, adjust=False).mean()
    
    # Calculate MACD line
    macd_line = ema_fast - ema_slow
    
    # Calculate Signal line
    signal_line = macd_line.ewm(span=signal, adjust=False).mean()
    
    # Calculate Histogram
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram

def calculate_bollinger_bands(data, window=20, num_std=2):
    """
    Calculate Bollinger Bands.
    
    Args:
        data (pd.DataFrame): Price data.
        window (int): Window size for moving average.
        num_std (int): Number of standard deviations.
        
    Returns:
        tuple: (Middle Band, Upper Band, Lower Band).
    """
    # Calculate middle band (SMA)
    middle_band = data['price'].rolling(window=window).mean()
    
    # Calculate standard deviation
    std = data['price'].rolling(window=window).std()
    
    # Calculate upper and lower bands
    upper_band = middle_band + (std * num_std)
    lower_band = middle_band - (std * num_std)
    
    return middle_band, upper_band, lower_band

def predict_price_trend(crypto_id, days=7):
    """
    Predict price trend for the next few days using simple linear regression.
    
    Args:
        crypto_id (str): Cryptocurrency ID.
        days (int): Number of days to predict.
        
    Returns:
        tuple: (Historical data with predictions, Prediction confidence).
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)  # Use 90 days of data for training
    
    # Get historical data
    historical_data = get_crypto_data(crypto_id, start_date, end_date)
    
    if historical_data.empty:
        return None, 0
    
    # Prepare data for prediction
    historical_data.reset_index(inplace=True)
    historical_data['day'] = (historical_data['timestamp'] - historical_data['timestamp'].min()).dt.days
    
    # Split data into features and target
    X = historical_data[['day']]
    y = historical_data['price']
    
    # Scale the features
    scaler_X = StandardScaler()
    X_scaled = scaler_X.fit_transform(X)
    
    # Scale the target
    scaler_y = StandardScaler()
    y_scaled = scaler_y.fit_transform(y.values.reshape(-1, 1))
    
    # Train a linear regression model
    model = LinearRegression()
    model.fit(X_scaled, y_scaled)
    
    # Calculate prediction confidence (R² score)
    confidence = model.score(X_scaled, y_scaled)
    
    # Generate future dates
    last_day = historical_data['day'].max()
    future_days = np.array(range(last_day + 1, last_day + days + 1)).reshape(-1, 1)
    
    # Scale future days
    future_days_scaled = scaler_X.transform(future_days)
    
    # Make predictions
    predictions_scaled = model.predict(future_days_scaled)
    
    # Inverse transform predictions
    predictions = scaler_y.inverse_transform(predictions_scaled)
    
    # Create DataFrame for future dates and predictions
    future_dates = [end_date + timedelta(days=i+1) for i in range(days)]
    future_df = pd.DataFrame({
        'timestamp': future_dates,
        'day': future_days.flatten(),
        'price': predictions.flatten(),
        'predicted': True
    })
    
    # Add 'predicted' column to historical data
    historical_data['predicted'] = False
    
    # Combine historical data and predictions
    combined_df = pd.concat([historical_data, future_df], ignore_index=True)
    
    return combined_df, confidence

def generate_technical_analysis(crypto_id, days=30):
    """
    Generate technical analysis data for a cryptocurrency.
    
    Args:
        crypto_id (str): Cryptocurrency ID.
        days (int): Number of days to analyze.
        
    Returns:
        tuple: (DataFrame with technical indicators, Analysis summary).
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Get historical data
    data = get_crypto_data(crypto_id, start_date, end_date)
    
    if data.empty:
        return None, "No data available for analysis."
    
    # Calculate technical indicators
    data['SMA_20'] = calculate_sma(data, 20)
    data['EMA_12'] = calculate_ema(data, 12)
    data['EMA_26'] = calculate_ema(data, 26)
    data['RSI'] = calculate_rsi(data, 14)
    
    macd_line, signal_line, histogram = calculate_macd(data)
    data['MACD'] = macd_line
    data['MACD_Signal'] = signal_line
    data['MACD_Histogram'] = histogram
    
    middle_band, upper_band, lower_band = calculate_bollinger_bands(data)
    data['BB_Middle'] = middle_band
    data['BB_Upper'] = upper_band
    data['BB_Lower'] = lower_band
    
    # Generate analysis summary
    summary = generate_analysis_summary(data)
    
    return data, summary

def generate_analysis_summary(data):
    """
    Generate a summary of technical analysis.
    
    Args:
        data (pd.DataFrame): Data with technical indicators.
        
    Returns:
        str: Analysis summary.
    """
    # Get the latest values
    latest = data.iloc[-1]
    
    # Current price and moving averages
    current_price = latest['price']
    sma_20 = latest['SMA_20']
    ema_12 = latest['EMA_12']
    ema_26 = latest['EMA_26']
    
    # RSI
    rsi = latest['RSI']
    
    # MACD
    macd = latest['MACD']
    macd_signal = latest['MACD_Signal']
    
    # Bollinger Bands
    bb_middle = latest['BB_Middle']
    bb_upper = latest['BB_Upper']
    bb_lower = latest['BB_Lower']
    
    # Generate summary
    summary = []
    
    # Moving Average analysis
    if current_price > sma_20:
        summary.append("Price is above the 20-day SMA, suggesting a bullish trend.")
    else:
        summary.append("Price is below the 20-day SMA, suggesting a bearish trend.")
    
    if ema_12 > ema_26:
        summary.append("12-day EMA is above 26-day EMA, indicating positive momentum.")
    else:
        summary.append("12-day EMA is below 26-day EMA, indicating negative momentum.")
    
    # RSI analysis
    if rsi > 70:
        summary.append(f"RSI is at {rsi:.2f}, suggesting the asset may be overbought.")
    elif rsi < 30:
        summary.append(f"RSI is at {rsi:.2f}, suggesting the asset may be oversold.")
    else:
        summary.append(f"RSI is at {rsi:.2f}, in the neutral zone.")
    
    # MACD analysis
    if macd > macd_signal:
        summary.append("MACD is above the signal line, indicating a bullish signal.")
    else:
        summary.append("MACD is below the signal line, indicating a bearish signal.")
    
    # Bollinger Bands analysis
    if current_price > bb_upper:
        summary.append("Price is above the upper Bollinger Band, suggesting potential overbought conditions.")
    elif current_price < bb_lower:
        summary.append("Price is below the lower Bollinger Band, suggesting potential oversold conditions.")
    else:
        bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) * 100
        summary.append(f"Price is within the Bollinger Bands, at {bb_position:.2f}% of the range.")
    
    return "\n".join(summary)
