import streamlit as st
import pandas as pd
from datetime import datetime

from crypto_data import get_current_prices
from utils import get_crypto_icon

# Page title
st.title("Price Alerts")

# Get current prices for the main cryptocurrencies
try:
    current_prices = get_current_prices(['bitcoin', 'ethereum', 'ripple', 'tether'])
    
    if not current_prices:
        st.warning("Unable to fetch current prices. Setting alerts may not work correctly.")
except Exception as e:
    st.error(f"Error retrieving price data: {str(e)}")
    current_prices = {}

# Create tabs for alert management
alert_tabs = st.tabs(["Create Alert", "Manage Alerts"])

with alert_tabs[0]:  # Create Alert
    st.subheader("Create New Price Alert")
    
    # Form for creating a new alert
    with st.form("create_alert_form"):
        crypto = st.selectbox(
            "Select Cryptocurrency",
            ["Bitcoin", "Ethereum", "XRP", "Tether"]
        )
        
        # Get current price for the selected cryptocurrency
        crypto_id = crypto.lower()
        if crypto_id == 'xrp':
            crypto_id = 'ripple'
            
        current_price = 0
        if crypto_id in current_prices:
            current_price = current_prices[crypto_id]['current_price']
            st.info(f"Current price: ${current_price:,.2f}")
        
        alert_type = st.radio(
            "Alert Type",
            ["Price Above", "Price Below"],
            horizontal=True
        )
        
        price_threshold = st.number_input(
            "Price Threshold (USD)",
            min_value=0.0,
            value=current_price,
            step=0.01,
            format="%.2f"
        )
        
        submit_button = st.form_submit_button("Create Alert")
        
        if submit_button:
            if price_threshold <= 0:
                st.error("Price threshold must be greater than zero.")
            else:
                # Create a new alert
                new_alert = {
                    'id': len(st.session_state.alerts) + 1,
                    'crypto': crypto,
                    'type': alert_type,
                    'threshold': price_threshold,
                    'created_at': datetime.now(),
                    'triggered': False
                }
                
                st.session_state.alerts.append(new_alert)
                
                st.success(f"Alert created: {alert_type} ${price_threshold:,.2f} for {crypto}")
                st.rerun()

with alert_tabs[1]:  # Manage Alerts
    st.subheader("Your Price Alerts")
    
    # Display existing alerts
    if st.session_state.alerts:
        # Check if any alerts have been triggered
        if current_prices:
            for i, alert in enumerate(st.session_state.alerts):
                if not alert['triggered']:
                    crypto_id = alert['crypto'].lower()
                    if crypto_id == 'xrp':
                        crypto_id = 'ripple'
                        
                    if crypto_id in current_prices:
                        current_price = current_prices[crypto_id]['current_price']
                        
                        # Check if alert should be triggered
                        if (alert['type'] == 'Price Above' and current_price >= alert['threshold']) or \
                           (alert['type'] == 'Price Below' and current_price <= alert['threshold']):
                            st.session_state.alerts[i]['triggered'] = True
        
        # Prepare alert data for display
        alert_data = []
        
        for alert in st.session_state.alerts:
            created_at = alert['created_at'].strftime("%Y-%m-%d %H:%M")
            
            status = "Active"
            if alert['triggered']:
                status = "Triggered"
            
            alert_data.append({
                'ID': alert['id'],
                'Cryptocurrency': f"{get_crypto_icon(alert['crypto'])} {alert['crypto']}",
                'Alert Type': alert['type'],
                'Price Threshold': f"${alert['threshold']:,.2f}",
                'Created': created_at,
                'Status': status
            })
        
        if alert_data:
            alerts_df = pd.DataFrame(alert_data)
            st.dataframe(alerts_df, use_container_width=True)
            
            # Highlight triggered alerts
            triggered_alerts = [alert for alert in st.session_state.alerts if alert['triggered']]
            if triggered_alerts:
                st.subheader("Triggered Alerts")
                for alert in triggered_alerts:
                    st.warning(
                        f"ALERT: {alert['crypto']} is now {alert['type'].lower().replace('price ', '')} ${alert['threshold']:,.2f}"
                    )
        
        # Form for deleting alerts
        with st.form("delete_alert_form"):
            st.subheader("Delete Alert")
            
            alert_ids = [alert['id'] for alert in st.session_state.alerts]
            alert_to_delete = st.selectbox("Select Alert to Delete", alert_ids)
            
            delete_button = st.form_submit_button("Delete Alert")
            
            if delete_button:
                # Find and remove the selected alert
                st.session_state.alerts = [alert for alert in st.session_state.alerts if alert['id'] != alert_to_delete]
                st.success(f"Alert #{alert_to_delete} has been deleted.")
                st.rerun()
        
        # Button to clear all alerts
        if st.button("Clear All Alerts"):
            st.session_state.alerts = []
            st.success("All alerts have been cleared.")
            st.rerun()
        
    else:
        st.info("You don't have any price alerts set up. Create a new alert to get notified when prices reach your specified thresholds.")
        st.image("https://images.unsplash.com/photo-1640143405373-bbb919afa0da", use_container_width=True)
