import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta

from crypto_data import get_crypto_data
from portfolio import calculate_portfolio_value, calculate_portfolio_allocation
from utils import format_currency, format_percentage, get_crypto_icon

# Function to calculate volatility
def calculate_volatility(data, window=14):
    """Calculate volatility (standard deviation of returns)"""
    returns = data.pct_change().dropna()
    volatility = returns.rolling(window=window).std() * np.sqrt(365)  # Annualized
    return volatility

# Function to calculate Sharpe ratio
def calculate_sharpe_ratio(data, risk_free_rate=0.02):
    """Calculate Sharpe ratio (returns / volatility)"""
    returns = data.pct_change().dropna()
    mean_returns = returns.mean() * 365  # Annualized returns
    volatility = returns.std() * np.sqrt(365)  # Annualized volatility
    return (mean_returns - risk_free_rate) / volatility

# Function to calculate max drawdown
def calculate_max_drawdown(data):
    """Calculate maximum drawdown"""
    cumulative = (1 + data.pct_change().fillna(0)).cumprod()
    running_max = cumulative.cummax()
    drawdown = (cumulative / running_max) - 1
    return drawdown.min()

# Function to calculate Value at Risk (VaR)
def calculate_var(data, confidence=0.95, timeframe=1):
    """Calculate Value at Risk"""
    returns = data.pct_change().dropna()
    var = np.percentile(returns, (1 - confidence) * 100) * np.sqrt(timeframe)
    return var

# Page title
st.title("Risk Assessment & Portfolio Metrics")

# Create tabs for different risk views
risk_tabs = st.tabs(["Portfolio Risk", "Cryptocurrency Comparison", "Risk Optimization"])

with risk_tabs[0]:  # Portfolio Risk
    st.subheader("Portfolio Risk Analysis")
    
    # Get portfolio data
    portfolio = st.session_state.portfolio
    
    # Check if portfolio has assets
    has_assets = False
    for crypto, data in portfolio.items():
        if data['amount'] > 0:
            has_assets = True
            break
    
    if not has_assets:
        st.info("You need to add assets to your portfolio to see risk analysis.")
        st.image("https://images.unsplash.com/photo-1460925895917-afdab827c52f", use_container_width=True)
    else:
        # Time period selection
        time_period = st.radio(
            "Analysis Period:",
            ["1 Month", "3 Months", "6 Months", "1 Year"],
            horizontal=True
        )
        
        # Map time period to days
        time_mapping = {
            "1 Month": 30,
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365
        }
        
        days = time_mapping[time_period]
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get historical data for cryptocurrencies in portfolio
        crypto_data = {}
        try:
            with st.spinner("Fetching historical data for risk calculations..."):
                for crypto, data in portfolio.items():
                    if data['amount'] > 0:
                        crypto_id = crypto.lower()
                        if crypto_id == 'xrp':
                            crypto_id = 'ripple'
                            
                        crypto_data[crypto] = get_crypto_data(crypto_id, start_date, end_date)['price']
        except Exception as e:
            st.error(f"Error retrieving historical data: {str(e)}")
        
        if crypto_data:
            # Create a DataFrame for all cryptocurrency prices
            price_df = pd.DataFrame(crypto_data)
            
            # Calculate portfolio values over time
            portfolio_df = pd.DataFrame(index=price_df.index)
            total_value = pd.Series(0, index=price_df.index)
            
            for crypto, data in portfolio.items():
                if data['amount'] > 0 and crypto in price_df.columns:
                    portfolio_df[crypto] = price_df[crypto] * data['amount']
                    total_value += portfolio_df[crypto]
            
            portfolio_df['Total'] = total_value
            
            # Calculate risk metrics
            st.subheader("Portfolio Risk Metrics")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                # Calculate portfolio volatility
                portfolio_volatility = calculate_volatility(portfolio_df['Total']).iloc[-1]
                st.metric(
                    "Annualized Volatility", 
                    f"{portfolio_volatility*100:.2f}%",
                    delta=None
                )
            
            with col2:
                # Calculate portfolio Sharpe ratio
                portfolio_sharpe = calculate_sharpe_ratio(portfolio_df['Total'])
                sharpe_delta = "+Good" if portfolio_sharpe > 1 else "-Poor" if portfolio_sharpe < 0 else "Neutral"
                st.metric(
                    "Sharpe Ratio", 
                    f"{portfolio_sharpe:.2f}",
                    delta=sharpe_delta
                )
            
            with col3:
                # Calculate max drawdown
                max_dd = calculate_max_drawdown(portfolio_df['Total'])
                st.metric(
                    "Maximum Drawdown", 
                    f"{max_dd*100:.2f}%",
                    delta=None
                )
            
            # VaR analysis
            st.subheader("Value at Risk (VaR) Analysis")
            
            col1, col2 = st.columns(2)
            
            with col1:
                confidence_level = st.slider(
                    "Confidence Level",
                    min_value=90,
                    max_value=99,
                    value=95,
                    step=1,
                    format="%d%%"
                )
                
                var_timeframe = st.radio(
                    "Time Horizon",
                    ["1 Day", "1 Week", "1 Month"],
                    horizontal=True
                )
                
                var_days = 1 if var_timeframe == "1 Day" else 7 if var_timeframe == "1 Week" else 30
            
            with col2:
                # Calculate VaR
                var = calculate_var(portfolio_df['Total'], confidence=confidence_level/100, timeframe=var_days)
                current_value = portfolio_df['Total'].iloc[-1]
                var_amount = abs(var * current_value)
                
                st.metric(
                    f"VaR ({confidence_level}%, {var_timeframe})", 
                    f"${var_amount:,.2f}",
                    f"{abs(var*100):.2f}% of portfolio"
                )
                
                st.write(f"""
                Based on historical data, there is a {confidence_level}% chance that your 
                portfolio will not lose more than ${var_amount:,.2f} over the next {var_timeframe.lower()}.
                """)
            
            # Risk allocation chart
            st.subheader("Risk Allocation")
            
            # Calculate contribution to volatility
            weights = {}
            total_value = sum(data['amount'] * price_df[crypto].iloc[-1] for crypto, data in portfolio.items() if data['amount'] > 0 and crypto in price_df.columns)
            
            for crypto, data in portfolio.items():
                if data['amount'] > 0 and crypto in price_df.columns:
                    weight = (data['amount'] * price_df[crypto].iloc[-1]) / total_value
                    weights[crypto] = weight
            
            # Calculate covariance matrix
            returns_df = price_df.pct_change().dropna()
            cov_matrix = returns_df.cov() * 252  # Annualized
            
            # Calculate contribution to risk
            risk_contribution = {}
            for crypto1 in weights:
                risk_contrib = 0
                for crypto2 in weights:
                    if crypto1 in cov_matrix.columns and crypto2 in cov_matrix.columns:
                        risk_contrib += weights[crypto1] * weights[crypto2] * cov_matrix.loc[crypto1, crypto2]
                
                risk_contribution[crypto1] = risk_contrib
            
            total_risk = sum(risk_contribution.values())
            risk_percent = {crypto: (contrib / total_risk) * 100 for crypto, contrib in risk_contribution.items()}
            
            # Create data for the risk allocation pie chart
            risk_data = []
            for crypto, percent in risk_percent.items():
                risk_data.append({
                    'Cryptocurrency': crypto,
                    'Risk Contribution (%)': percent,
                    'Weight (%)': weights[crypto] * 100
                })
            
            if risk_data:
                risk_df = pd.DataFrame(risk_data)
                
                fig = px.pie(
                    risk_df, 
                    values='Risk Contribution (%)', 
                    names='Cryptocurrency', 
                    title='Portfolio Risk Contribution',
                    hover_data=['Weight (%)'],
                    color_discrete_sequence=px.colors.qualitative.Bold
                )
                
                fig.update_traces(
                    textposition='inside', 
                    textinfo='percent+label',
                    hovertemplate='<b>%{label}</b><br>Risk Contribution: %{value:.2f}%<br>Weight: %{customdata[0]:.2f}%<extra></extra>'
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # Compare risk contribution vs allocation
                st.subheader("Risk vs Allocation Comparison")
                
                compare_df = pd.DataFrame({
                    'Cryptocurrency': risk_df['Cryptocurrency'],
                    'Risk Contribution (%)': risk_df['Risk Contribution (%)'],
                    'Portfolio Weight (%)': risk_df['Weight (%)']
                })
                
                fig = go.Figure(data=[
                    go.Bar(
                        name='Risk Contribution',
                        x=compare_df['Cryptocurrency'],
                        y=compare_df['Risk Contribution (%)'],
                        marker_color='#F44336'
                    ),
                    go.Bar(
                        name='Portfolio Weight',
                        x=compare_df['Cryptocurrency'],
                        y=compare_df['Portfolio Weight (%)'],
                        marker_color='#2196F3'
                    )
                ])
                
                fig.update_layout(
                    barmode='group',
                    title='Risk Contribution vs Portfolio Weight',
                    xaxis_title='Cryptocurrency',
                    yaxis_title='Percentage (%)',
                    template='plotly_white',
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # Risk interpretation
                st.subheader("Risk Analysis Interpretation")
                
                # Find highest risk contributor
                highest_risk = max(risk_percent.items(), key=lambda x: x[1])
                
                # Compare risk vs weight
                risk_weight_diff = {}
                for crypto in weights:
                    if crypto in risk_percent:
                        risk_weight_diff[crypto] = risk_percent[crypto] - (weights[crypto] * 100)
                
                most_inefficient = max(risk_weight_diff.items(), key=lambda x: x[1])
                
                st.write(f"""
                ### Key Insights:
                
                - **{highest_risk[0]}** contributes the most to your portfolio risk ({highest_risk[1]:.2f}%)
                
                - **{most_inefficient[0]}** has the largest difference between risk contribution and portfolio weight ({most_inefficient[1]:+.2f}%), 
                indicating this asset may be adding disproportionate risk relative to its allocation
                
                - Your portfolio has an overall Sharpe Ratio of **{portfolio_sharpe:.2f}**, which indicates 
                {'good' if portfolio_sharpe > 1 else 'poor' if portfolio_sharpe < 0 else 'average'} 
                risk-adjusted returns
                
                ### Recommendations:
                
                - Consider {'reducing' if most_inefficient[1] > 0 else 'increasing'} your allocation to **{most_inefficient[0]}** 
                to better balance risk and return
                
                - Monitor your Maximum Drawdown of **{max_dd*100:.2f}%** to ensure it stays within your risk tolerance
                
                - Review your Value at Risk (VaR) of **${var_amount:,.2f}** to ensure you're comfortable with potential losses
                """)
            else:
                st.warning("Unable to calculate risk metrics with the available data.")
        else:
            st.error("Unable to retrieve historical data for risk calculations. Please try again later.")

with risk_tabs[1]:  # Cryptocurrency Comparison
    st.subheader("Cryptocurrency Risk Comparison")
    
    # Select cryptocurrencies to compare
    selected_cryptos = st.multiselect(
        "Select cryptocurrencies to compare",
        ["Bitcoin", "Ethereum", "XRP", "Tether"],
        default=["Bitcoin", "Ethereum"]
    )
    
    if not selected_cryptos:
        st.info("Please select at least one cryptocurrency for comparison.")
    else:
        # Time period selection
        time_period = st.radio(
            "Analysis Period:",
            ["1 Month", "3 Months", "6 Months", "1 Year"],
            horizontal=True,
            key="compare_time_period"
        )
        
        # Map time period to days
        time_mapping = {
            "1 Month": 30,
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365
        }
        
        days = time_mapping[time_period]
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get historical data for selected cryptocurrencies
        crypto_data = {}
        try:
            with st.spinner("Fetching historical data for risk comparison..."):
                for crypto in selected_cryptos:
                    crypto_id = crypto.lower()
                    if crypto_id == 'xrp':
                        crypto_id = 'ripple'
                        
                    crypto_data[crypto] = get_crypto_data(crypto_id, start_date, end_date)['price']
        except Exception as e:
            st.error(f"Error retrieving historical data: {str(e)}")
        
        if crypto_data:
            # Create a DataFrame for all cryptocurrency prices
            price_df = pd.DataFrame(crypto_data)
            
            # Calculate risk metrics for each cryptocurrency
            risk_metrics = []
            
            for crypto in selected_cryptos:
                if crypto in price_df.columns:
                    volatility = calculate_volatility(price_df[crypto]).iloc[-1] * 100  # Convert to percentage
                    sharpe = calculate_sharpe_ratio(price_df[crypto])
                    max_dd = calculate_max_drawdown(price_df[crypto]) * 100  # Convert to percentage
                    var_95 = abs(calculate_var(price_df[crypto], confidence=0.95, timeframe=1) * 100)  # 1-day VaR, as percentage
                    
                    risk_metrics.append({
                        'Cryptocurrency': crypto,
                        'Volatility (%)': volatility,
                        'Sharpe Ratio': sharpe,
                        'Max Drawdown (%)': max_dd,
                        'VaR 95% (%)': var_95
                    })
            
            if risk_metrics:
                risk_df = pd.DataFrame(risk_metrics)
                
                # Format the dataframe for display
                display_df = risk_df.copy()
                display_df['Volatility (%)'] = display_df['Volatility (%)'].apply(lambda x: f"{x:.2f}%")
                display_df['Sharpe Ratio'] = display_df['Sharpe Ratio'].apply(lambda x: f"{x:.2f}")
                display_df['Max Drawdown (%)'] = display_df['Max Drawdown (%)'].apply(lambda x: f"{x:.2f}%")
                display_df['VaR 95% (%)'] = display_df['VaR 95% (%)'].apply(lambda x: f"{x:.2f}%")
                
                st.dataframe(display_df, use_container_width=True)
                
                # Create visualizations
                st.subheader("Risk Metric Comparison")
                
                # Radar chart for risk metrics
                # Prepare data for radar chart
                categories = ['Volatility', 'Inverse Sharpe', 'Max Drawdown', 'VaR 95%']
                
                fig = go.Figure()
                
                for crypto in selected_cryptos:
                    if crypto in risk_df['Cryptocurrency'].values:
                        crypto_data = risk_df[risk_df['Cryptocurrency'] == crypto].iloc[0]
                        
                        # Normalize values for radar chart (0-1 scale, higher is more risky)
                        volatility_norm = crypto_data['Volatility (%)'] / risk_df['Volatility (%)'].max()
                        # Inverse Sharpe (lower Sharpe is higher risk)
                        if crypto_data['Sharpe Ratio'] <= 0:
                            inverse_sharpe_norm = 1.0  # Max risk for negative Sharpe
                        else:
                            max_sharpe = max(1.0, risk_df['Sharpe Ratio'].max())
                            inverse_sharpe_norm = 1.0 - (crypto_data['Sharpe Ratio'] / max_sharpe)
                        
                        drawdown_norm = abs(crypto_data['Max Drawdown (%)']) / abs(risk_df['Max Drawdown (%)'].max())
                        var_norm = crypto_data['VaR 95% (%)'] / risk_df['VaR 95% (%)'].max()
                        
                        fig.add_trace(go.Scatterpolar(
                            r=[volatility_norm, inverse_sharpe_norm, drawdown_norm, var_norm],
                            theta=categories,
                            fill='toself',
                            name=crypto
                        ))
                
                fig.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 1]
                        )
                    ),
                    title="Risk Profile Comparison (Higher = More Risk)",
                    showlegend=True,
                    height=500
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # Bar charts for individual metrics
                st.subheader("Individual Risk Metric Comparison")
                
                metric_tabs = st.tabs(["Volatility", "Sharpe Ratio", "Max Drawdown", "VaR 95%"])
                
                with metric_tabs[0]:  # Volatility
                    fig = px.bar(
                        risk_df,
                        x='Cryptocurrency',
                        y='Volatility (%)',
                        title='Annualized Volatility (%)',
                        color='Cryptocurrency',
                        text_auto='.2f'
                    )
                    
                    fig.update_layout(showlegend=False, height=400)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    st.write("""
                    **Volatility** measures how much the price of an asset fluctuates over time. 
                    Higher volatility indicates greater price swings and potentially higher risk.
                    """)
                
                with metric_tabs[1]:  # Sharpe Ratio
                    fig = px.bar(
                        risk_df,
                        x='Cryptocurrency',
                        y='Sharpe Ratio',
                        title='Sharpe Ratio',
                        color='Cryptocurrency',
                        text_auto='.2f'
                    )
                    
                    # Add a line for "good" Sharpe ratio
                    fig.add_shape(
                        type="line",
                        x0=-0.5,
                        y0=1,
                        x1=len(selected_cryptos) - 0.5,
                        y1=1,
                        line=dict(color="green", width=2, dash="dash")
                    )
                    
                    fig.add_annotation(
                        x=len(selected_cryptos) - 0.5,
                        y=1.05,
                        text="Good Sharpe Ratio (1.0+)",
                        showarrow=False,
                        font=dict(color="green")
                    )
                    
                    fig.update_layout(showlegend=False, height=400)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    st.write("""
                    **Sharpe Ratio** measures the excess return per unit of risk. 
                    A higher Sharpe Ratio indicates better risk-adjusted returns.
                    - Sharpe Ratio > 1: Good
                    - Sharpe Ratio 0-1: Acceptable
                    - Sharpe Ratio < 0: Poor (returns less than risk-free rate)
                    """)
                
                with metric_tabs[2]:  # Max Drawdown
                    fig = px.bar(
                        risk_df,
                        x='Cryptocurrency',
                        y='Max Drawdown (%)',
                        title='Maximum Drawdown (%)',
                        color='Cryptocurrency',
                        text_auto='.2f'
                    )
                    
                    fig.update_layout(showlegend=False, height=400)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    st.write("""
                    **Maximum Drawdown** is the largest percentage drop from a peak to a subsequent trough.
                    It represents the worst-case scenario and helps assess downside risk.
                    Lower (less negative) values are better.
                    """)
                
                with metric_tabs[3]:  # VaR
                    fig = px.bar(
                        risk_df,
                        x='Cryptocurrency',
                        y='VaR 95% (%)',
                        title='Value at Risk (VaR) 95%, 1 Day (%)',
                        color='Cryptocurrency',
                        text_auto='.2f'
                    )
                    
                    fig.update_layout(showlegend=False, height=400)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    st.write("""
                    **Value at Risk (VaR)** estimates the potential loss in value over a defined period for a given
                    confidence level. A 95% 1-day VaR of 5% means there's a 95% chance you won't lose more than 5%
                    of your investment in a single day.
                    """)
                
                # Risk-return scatter plot
                st.subheader("Risk vs. Return Analysis")
                
                returns_df = price_df.pct_change().dropna()
                
                risk_return_data = []
                for crypto in selected_cryptos:
                    if crypto in returns_df.columns:
                        annual_return = returns_df[crypto].mean() * 365 * 100  # Annualized return as percentage
                        annual_volatility = returns_df[crypto].std() * np.sqrt(365) * 100  # Annualized volatility as percentage
                        
                        risk_return_data.append({
                            'Cryptocurrency': crypto,
                            'Annual Return (%)': annual_return,
                            'Annual Volatility (%)': annual_volatility
                        })
                
                if risk_return_data:
                    risk_return_df = pd.DataFrame(risk_return_data)
                    
                    fig = px.scatter(
                        risk_return_df,
                        x='Annual Volatility (%)',
                        y='Annual Return (%)',
                        text='Cryptocurrency',
                        color='Cryptocurrency',
                        size=[30] * len(risk_return_df),  # Fixed size for all points
                        title='Risk vs. Return',
                        height=500
                    )
                    
                    # Add labels to points
                    fig.update_traces(
                        textposition='top center',
                        hovertemplate='<b>%{text}</b><br>Return: %{y:.2f}%<br>Volatility: %{x:.2f}%<extra></extra>'
                    )
                    
                    # Add a line from origin with slope = 1 (representing equal risk/return)
                    max_vol = risk_return_df['Annual Volatility (%)'].max() * 1.2
                    fig.add_shape(
                        type="line",
                        x0=0,
                        y0=0,
                        x1=max_vol,
                        y1=max_vol,
                        line=dict(color="gray", width=1, dash="dot")
                    )
                    
                    fig.update_layout(
                        xaxis_title='Risk (Annual Volatility %)',
                        yaxis_title='Return (Annual %)',
                        showlegend=True
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    st.write("""
                    This chart shows the relationship between risk (volatility) and return for each cryptocurrency.
                    
                    - Position in the top-left quadrant indicates high returns with low risk (ideal)
                    - Position in the top-right quadrant indicates high returns with high risk
                    - Position in the bottom-left quadrant indicates low returns with low risk
                    - Position in the bottom-right quadrant indicates low returns with high risk (worst)
                    
                    The dotted line represents equal risk and return - points above this line have returns that outpace their risk.
                    """)
            else:
                st.warning("Unable to calculate risk metrics with the available data.")
        else:
            st.error("Unable to retrieve historical data for risk calculations. Please try again later.")

with risk_tabs[2]:  # Risk Optimization
    st.subheader("Portfolio Optimization")
    
    # Get portfolio data
    portfolio = st.session_state.portfolio
    
    # Check if portfolio has assets
    has_assets = False
    for crypto, data in portfolio.items():
        if data['amount'] > 0:
            has_assets = True
            break
    
    if not has_assets:
        st.info("You need to add assets to your portfolio to see optimization options.")
        st.image("https://images.unsplash.com/photo-1573164574511-73c773193279", use_container_width=True)
    else:
        st.write("""
        Portfolio optimization seeks to find the best asset allocation based on your risk and return objectives.
        You can optimize for:
        
        - **Maximum Sharpe Ratio**: Best risk-adjusted returns
        - **Minimum Volatility**: Lowest overall portfolio risk
        - **Maximum Return**: Highest expected return (at a given risk level)
        """)
        
        # Optimization parameters
        optimization_type = st.radio(
            "Optimization Objective",
            ["Maximum Sharpe Ratio", "Minimum Volatility", "Target Return"],
            horizontal=True
        )
        
        # Time period for historical data
        time_period = st.radio(
            "Historical Data Period:",
            ["3 Months", "6 Months", "1 Year"],
            horizontal=True,
            key="optimize_time_period"
        )
        
        # Map time period to days
        time_mapping = {
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365
        }
        
        days = time_mapping[time_period]
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get historical data for cryptocurrencies in portfolio
        crypto_data = {}
        try:
            with st.spinner("Fetching historical data for optimization calculations..."):
                for crypto, data in portfolio.items():
                    if data['amount'] > 0:
                        crypto_id = crypto.lower()
                        if crypto_id == 'xrp':
                            crypto_id = 'ripple'
                            
                        crypto_data[crypto] = get_crypto_data(crypto_id, start_date, end_date)['price']
        except Exception as e:
            st.error(f"Error retrieving historical data: {str(e)}")
        
        if crypto_data:
            # Create a DataFrame for all cryptocurrency prices
            price_df = pd.DataFrame(crypto_data)
            
            # Calculate returns
            returns_df = price_df.pct_change().dropna()
            
            # Calculate mean returns and covariance matrix
            mean_returns = returns_df.mean() * 252  # Annualized
            cov_matrix = returns_df.cov() * 252  # Annualized
            
            # Current portfolio weights
            total_value = 0
            for crypto, data in portfolio.items():
                if crypto in price_df.columns:
                    total_value += data['amount'] * price_df[crypto].iloc[-1]
            
            current_weights = {}
            for crypto, data in portfolio.items():
                if data['amount'] > 0 and crypto in price_df.columns:
                    current_weights[crypto] = (data['amount'] * price_df[crypto].iloc[-1]) / total_value
            
            # Display current portfolio metrics
            st.subheader("Current Portfolio Metrics")
            
            current_portfolio_return = sum(current_weights.get(crypto, 0) * ret for crypto, ret in mean_returns.items())
            
            # Calculate current portfolio volatility
            current_portfolio_volatility = 0
            for crypto1, weight1 in current_weights.items():
                for crypto2, weight2 in current_weights.items():
                    if crypto1 in cov_matrix.index and crypto2 in cov_matrix.columns:
                        current_portfolio_volatility += weight1 * weight2 * cov_matrix.loc[crypto1, crypto2]
            
            current_portfolio_volatility = np.sqrt(current_portfolio_volatility)
            current_sharpe_ratio = current_portfolio_return / current_portfolio_volatility if current_portfolio_volatility > 0 else 0
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "Expected Annual Return", 
                    f"{current_portfolio_return*100:.2f}%",
                    delta=None
                )
            
            with col2:
                st.metric(
                    "Expected Volatility", 
                    f"{current_portfolio_volatility*100:.2f}%",
                    delta=None
                )
            
            with col3:
                st.metric(
                    "Sharpe Ratio", 
                    f"{current_sharpe_ratio:.2f}",
                    delta=None
                )
            
            # Simple portfolio optimization - this is a basic implementation
            # For a real application, you might want to use optimization libraries like scipy.optimize
            
            # Generate random portfolios for optimization
            num_portfolios = 5000
            results = np.zeros((3, num_portfolios))
            weights_record = []
            
            st.write("Calculating optimal portfolios...")
            
            # Create efficient frontier with random portfolios
            crypto_list = list(mean_returns.index)
            num_assets = len(crypto_list)
            
            for i in range(num_portfolios):
                # Random weights
                weights = np.random.random(num_assets)
                weights /= np.sum(weights)
                
                # Save weights
                weights_record.append(weights)
                
                # Calculate portfolio return
                portfolio_return = np.sum(weights * mean_returns)
                
                # Calculate portfolio volatility
                portfolio_volatility = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
                
                # Calculate Sharpe Ratio
                sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
                
                # Save results
                results[0, i] = portfolio_return
                results[1, i] = portfolio_volatility
                results[2, i] = sharpe_ratio
            
            # Convert results to DataFrame
            columns = ['Return', 'Volatility', 'Sharpe']
            results_df = pd.DataFrame(results.T, columns=columns)
            
            # Add weights to results
            for i, crypto in enumerate(crypto_list):
                results_df[crypto] = [w[i] for w in weights_record]
            
            # Find optimal portfolios
            max_sharpe_idx = results_df['Sharpe'].idxmax()
            min_volatility_idx = results_df['Volatility'].idxmin()
            
            # Initialize variables for target return
            target_return = 0.0
            target_return_idx = 0
            
            # Define target return for optimization
            if optimization_type == "Target Return":
                target_return = st.slider(
                    "Target Annual Return (%)",
                    min_value=float(np.floor(results_df['Return'].min() * 100)),
                    max_value=float(np.ceil(results_df['Return'].max() * 100)),
                    value=float(np.ceil(current_portfolio_return * 100)),
                    step=1.0
                ) / 100
                
                # Find portfolio closest to target return
                target_return_idx = (results_df['Return'] - target_return).abs().idxmin()
            
            # Get optimal weights based on optimization type
            if optimization_type == "Maximum Sharpe Ratio":
                optimal_idx = max_sharpe_idx
                optimal_type = "Maximum Sharpe Ratio"
            elif optimization_type == "Minimum Volatility":
                optimal_idx = min_volatility_idx
                optimal_type = "Minimum Volatility"
            else:  # Target Return
                optimal_idx = target_return_idx
                optimal_type = f"Target Return ({target_return*100:.2f}%)"
            
            optimal_weights = {}
            for crypto in crypto_list:
                optimal_weights[crypto] = results_df.loc[optimal_idx, crypto]
            
            # Display efficient frontier
            st.subheader("Efficient Frontier")
            
            fig = go.Figure()
            
            # Plot random portfolios
            fig.add_trace(
                go.Scatter(
                    x=results_df['Volatility'] * 100,
                    y=results_df['Return'] * 100,
                    mode='markers',
                    marker=dict(
                        size=5,
                        color=results_df['Sharpe'],
                        colorscale='Viridis',
                        colorbar=dict(title='Sharpe Ratio'),
                        showscale=True
                    ),
                    text=[f"Sharpe: {s:.2f}" for s in results_df['Sharpe']],
                    name='Portfolios'
                )
            )
            
            # Mark the current portfolio
            fig.add_trace(
                go.Scatter(
                    x=[current_portfolio_volatility * 100],
                    y=[current_portfolio_return * 100],
                    mode='markers',
                    marker=dict(
                        color='red',
                        size=12,
                        symbol='star'
                    ),
                    name='Current Portfolio'
                )
            )
            
            # Mark the optimal portfolio
            optimal_return = results_df.loc[optimal_idx, 'Return']
            optimal_volatility = results_df.loc[optimal_idx, 'Volatility']
            
            fig.add_trace(
                go.Scatter(
                    x=[optimal_volatility * 100],
                    y=[optimal_return * 100],
                    mode='markers',
                    marker=dict(
                        color='green',
                        size=12,
                        symbol='diamond'
                    ),
                    name=f'Optimal Portfolio ({optimal_type})'
                )
            )
            
            # Mark max Sharpe and min volatility portfolios
            if optimal_type != "Maximum Sharpe Ratio":
                max_sharpe_return = results_df.loc[max_sharpe_idx, 'Return']
                max_sharpe_volatility = results_df.loc[max_sharpe_idx, 'Volatility']
                
                fig.add_trace(
                    go.Scatter(
                        x=[max_sharpe_volatility * 100],
                        y=[max_sharpe_return * 100],
                        mode='markers',
                        marker=dict(
                            color='blue',
                            size=10,
                            symbol='circle'
                        ),
                        name='Max Sharpe Ratio'
                    )
                )
            
            if optimal_type != "Minimum Volatility":
                min_vol_return = results_df.loc[min_volatility_idx, 'Return']
                min_vol_volatility = results_df.loc[min_volatility_idx, 'Volatility']
                
                fig.add_trace(
                    go.Scatter(
                        x=[min_vol_volatility * 100],
                        y=[min_vol_return * 100],
                        mode='markers',
                        marker=dict(
                            color='orange',
                            size=10,
                            symbol='circle'
                        ),
                        name='Min Volatility'
                    )
                )
            
            fig.update_layout(
                title='Portfolio Optimization: Risk vs Return',
                xaxis_title='Expected Volatility (%)',
                yaxis_title='Expected Return (%)',
                height=600,
                template='plotly_white'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Display optimal portfolio
            st.subheader(f"Optimal Portfolio Allocation ({optimal_type})")
            
            optimal_return = results_df.loc[optimal_idx, 'Return']
            optimal_volatility = results_df.loc[optimal_idx, 'Volatility']
            optimal_sharpe = results_df.loc[optimal_idx, 'Sharpe']
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "Expected Annual Return", 
                    f"{optimal_return*100:.2f}%",
                    f"{(optimal_return - current_portfolio_return)*100:+.2f}% vs. current"
                )
            
            with col2:
                st.metric(
                    "Expected Volatility", 
                    f"{optimal_volatility*100:.2f}%",
                    f"{(optimal_volatility - current_portfolio_volatility)*100:+.2f}% vs. current"
                )
            
            with col3:
                st.metric(
                    "Sharpe Ratio", 
                    f"{optimal_sharpe:.2f}",
                    f"{(optimal_sharpe - current_sharpe_ratio):+.2f} vs. current"
                )
            
            # Display optimal weights vs current weights
            weights_comparison = []
            
            for crypto in crypto_list:
                weights_comparison.append({
                    'Cryptocurrency': crypto,
                    'Current Weight (%)': current_weights.get(crypto, 0) * 100,
                    'Optimal Weight (%)': optimal_weights[crypto] * 100,
                    'Difference (%)': (optimal_weights[crypto] - current_weights.get(crypto, 0)) * 100
                })
            
            weights_df = pd.DataFrame(weights_comparison)
            
            # Sort by optimal weight
            weights_df = weights_df.sort_values('Optimal Weight (%)', ascending=False)
            
            # Format for display
            display_weights_df = weights_df.copy()
            display_weights_df['Current Weight (%)'] = display_weights_df['Current Weight (%)'].apply(lambda x: f"{x:.2f}%")
            display_weights_df['Optimal Weight (%)'] = display_weights_df['Optimal Weight (%)'].apply(lambda x: f"{x:.2f}%")
            display_weights_df['Difference (%)'] = display_weights_df['Difference (%)'].apply(lambda x: f"{x:+.2f}%")
            
            st.dataframe(display_weights_df, use_container_width=True)
            
            # Plot weight comparison
            fig = go.Figure(data=[
                go.Bar(
                    name='Current Weights',
                    x=weights_df['Cryptocurrency'],
                    y=weights_df['Current Weight (%)'],
                    marker_color='#2196F3'
                ),
                go.Bar(
                    name='Optimal Weights',
                    x=weights_df['Cryptocurrency'],
                    y=weights_df['Optimal Weight (%)'],
                    marker_color='#4CAF50'
                )
            ])
            
            fig.update_layout(
                barmode='group',
                title='Current vs. Optimal Portfolio Weights',
                xaxis_title='Cryptocurrency',
                yaxis_title='Weight (%)',
                template='plotly_white',
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Recommendations based on optimization
            st.subheader("Portfolio Adjustment Recommendations")
            
            recommendations = []
            
            for _, row in weights_df.iterrows():
                crypto = row['Cryptocurrency']
                current = row['Current Weight (%)']
                optimal = row['Optimal Weight (%)']
                diff = row['Difference (%)']
                
                if abs(diff) > 5:  # Only recommend significant changes
                    action = "Increase" if diff > 0 else "Decrease"
                    recommendations.append({
                        'Cryptocurrency': crypto,
                        'Action': action,
                        'Current Weight': f"{current:.2f}%",
                        'Target Weight': f"{optimal:.2f}%",
                        'Change Required': f"{abs(diff):.2f}%"
                    })
            
            if recommendations:
                recommendations_df = pd.DataFrame(recommendations)
                st.dataframe(recommendations_df, use_container_width=True)
                
                st.write(f"""
                ### Key Adjustment Summary:
                
                To achieve the {optimal_type} portfolio:
                
                - Expected return would {'increase' if optimal_return > current_portfolio_return else 'decrease'} 
                  by {abs(optimal_return - current_portfolio_return)*100:.2f}%
                  
                - Expected volatility would {'increase' if optimal_volatility > current_portfolio_volatility else 'decrease'} 
                  by {abs(optimal_volatility - current_portfolio_volatility)*100:.2f}%
                  
                - Sharpe ratio would {'improve' if optimal_sharpe > current_sharpe_ratio else 'decrease'} 
                  from {current_sharpe_ratio:.2f} to {optimal_sharpe:.2f}
                
                These adjustments are based on historical data and modern portfolio theory. Remember that past performance
                is not necessarily indicative of future results.
                """)
            else:
                st.success("Your current portfolio allocation is already close to optimal based on the selected criteria!")
        else:
            st.error("Unable to retrieve historical data for optimization. Please try again later.")