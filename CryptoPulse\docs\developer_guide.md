# CryptoTracker: Developer Guide

## Project Overview

CryptoTracker is a Streamlit-based application for cryptocurrency analysis and portfolio management. This document provides technical details for developers working on the project.

## Environment Setup

### Requirements
- Python 3.11+
- Streamlit
- Additional packages: pandas, numpy, plotly, requests, scikit-learn, pillow

### Installation
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run the application: `streamlit run app.py --server.port 5000`

## Project Structure

```
├── .streamlit/                # Streamlit configuration
│   └── config.toml            # Server and theme settings
├── pages/                     # Streamlit pages
│   ├── ai_insights.py         # AI predictions and technical analysis
│   ├── market_overview.py     # Market data and charts
│   ├── portfolio_dashboard.py # Portfolio management
│   └── price_alerts.py        # Price alert system
├── app.py                     # Main application entry point
├── analysis.py                # Technical analysis functions
├── crypto_data.py             # Data fetching and processing
├── portfolio.py               # Portfolio calculation functions
└── utils.py                   # Utility functions
```

## Key Components

### `app.py`
The main entry point for the application. Handles:
- Page configuration and setup
- Session state initialization
- Navigation sidebar
- Home page content

### `crypto_data.py`
Responsible for fetching cryptocurrency data from external APIs.

Key functions:
- `get_crypto_data()`: Fetches historical price data
- `get_current_prices()`: Gets current price and 24h changes
- `get_crypto_details()`: Gets detailed information about a cryptocurrency
- `get_top_cryptocurrencies()`: Gets list of top coins by market cap

### `portfolio.py`
Manages portfolio calculations and transactions.

Key functions:
- `calculate_portfolio_value()`: Calculates total portfolio value
- `calculate_portfolio_allocation()`: Determines percentage allocation
- `calculate_portfolio_performance()`: Computes profit/loss metrics
- `add_transaction()`: Records buy/sell transactions
- `get_historical_portfolio_value()`: Calculates portfolio value over time

### `analysis.py`
Implements technical analysis indicators and predictions.

Key functions:
- `calculate_sma()`: Simple Moving Average
- `calculate_ema()`: Exponential Moving Average 
- `calculate_rsi()`: Relative Strength Index
- `calculate_macd()`: Moving Average Convergence Divergence
- `calculate_bollinger_bands()`: Bollinger Bands
- `predict_price_trend()`: ML-based price prediction
- `generate_technical_analysis()`: Comprehensive technical analysis

### `utils.py`
Utility functions for formatting and display.

Key functions:
- `format_currency()`: Format numbers as currency
- `format_percentage()`: Format numbers as percentages
- `get_color_from_value()`: Get color based on positive/negative value
- `load_image()`: Load images from URLs
- `get_crypto_icon()`: Get cryptocurrency icon

## Page Structure

### `pages/market_overview.py`
Shows comprehensive market data including:
- Current prices for key cryptocurrencies
- Interactive price comparison charts
- Market metrics (market cap, volume, etc.)
- Market insights and trends

### `pages/portfolio_dashboard.py`
Portfolio management features including:
- Portfolio summary with current value
- Asset allocation visualization
- Holdings breakdown table
- Historical portfolio value charting
- Transaction management

### `pages/price_alerts.py`
Price alert system including:
- Alert creation interface
- Alert management and status tracking
- Triggered alert notifications

### `pages/ai_insights.py`
AI-powered analysis features:
- Price predictions using machine learning
- Technical analysis with various indicators
- Sentiment analysis placeholder

## Data Flow

1. **Data Retrieval**: `crypto_data.py` fetches data from CoinGecko API
2. **Data Processing**: Raw data is processed into pandas DataFrames
3. **Analysis**: `analysis.py` performs calculations on the processed data
4. **Visualization**: Plotly creates interactive charts from the analyzed data
5. **User Interface**: Streamlit components display the visualizations and data
6. **State Management**: Streamlit session state maintains user data between sessions

## API Integration

### CoinGecko API
The application uses the free CoinGecko API with the following endpoints:
- `/coins/{id}/market_chart/range`: Historical price data
- `/coins/markets`: Current prices and market data
- `/coins/{id}`: Detailed information about a coin

Rate limiting is handled through a simple caching mechanism in the `get_with_cache()` function.

## Session State Management

The application uses Streamlit's session state to maintain data between user interactions:
- `st.session_state.portfolio`: User's cryptocurrency holdings
- `st.session_state.alerts`: User's configured price alerts

## Error Handling

The application implements error handling at multiple levels:
- API request errors are caught and handled gracefully
- Data processing errors display user-friendly messages
- Missing data conditions show appropriate information messages

## Performance Considerations

- **API Caching**: Responses are cached to reduce API calls
- **Efficient Data Processing**: Pandas operations are optimized
- **Lazy Loading**: Data is fetched only when needed

## Deployment Notes

For production deployment:
1. Ensure the `.streamlit/config.toml` contains proper server settings
2. Set `headless = true` and `address = "0.0.0.0"`
3. Configure port based on hosting environment (default: 5000)

## Future Development

Potential technical improvements:
1. **Database Integration**: Move from session state to persistent storage
2. **API Key Authentication**: Implement API key management
3. **Advanced Models**: Enhance ML prediction capabilities
4. **Testing Framework**: Add unit and integration tests
5. **WebSocket Support**: Real-time data updates