import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta

from crypto_data import get_crypto_data, get_current_prices
from analysis import calculate_sma, calculate_ema, calculate_rsi, calculate_macd, calculate_bollinger_bands
from utils import format_currency, get_crypto_icon

def calculate_fibonacci_retracement(high, low):
    """Calculate Fibonacci retracement levels"""
    diff = high - low
    levels = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]
    retracements = [high - (diff * level) for level in levels]
    return dict(zip(levels, retracements))

def calculate_pivots(high, low, close):
    """Calculate pivot points (classic)"""
    pivot = (high + low + close) / 3
    s1 = (2 * pivot) - high
    s2 = pivot - (high - low)
    s3 = low - 2 * (high - pivot)
    r1 = (2 * pivot) - low
    r2 = pivot + (high - low)
    r3 = high + 2 * (pivot - low)
    
    return {
        'pivot': pivot,
        's1': s1,
        's2': s2,
        's3': s3,
        'r1': r1,
        'r2': r2,
        'r3': r3
    }

# Page title
st.title("Advanced Technical Analysis")

# Cryptocurrency selector
selected_crypto = st.selectbox(
    "Select Cryptocurrency",
    ["Bitcoin", "Ethereum", "XRP", "Tether"]
)

# Map cryptocurrency names to IDs
crypto_mapping = {
    'Bitcoin': 'bitcoin',
    'Ethereum': 'ethereum',
    'XRP': 'ripple',
    'Tether': 'tether'
}

selected_crypto_id = crypto_mapping.get(selected_crypto)

# Time period selector
col1, col2 = st.columns([2, 1])

with col1:
    time_period = st.radio(
        "Time period:",
        ["1 Week", "1 Month", "3 Months", "6 Months", "1 Year"],
        horizontal=True
    )
    
    # Map time period to days
    time_mapping = {
        "1 Week": 7,
        "1 Month": 30,
        "3 Months": 90,
        "6 Months": 180,
        "1 Year": 365
    }
    
    days = time_mapping[time_period]
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

with col2:
    st.metric(
        "Current Analysis", 
        f"{selected_crypto}",
        f"{time_period} Chart"
    )

# Create tabs for different analysis tools
analysis_tabs = st.tabs([
    "Multi-Indicator Analysis", 
    "Fibonacci & Support/Resistance", 
    "Correlation Analysis", 
    "Volume Profile"
])

# Get the cryptocurrency data
try:
    with st.spinner(f"Loading {selected_crypto} data..."):
        data = get_crypto_data(selected_crypto_id, start_date, end_date)
        
    if data.empty:
        st.error(f"No data available for {selected_crypto} in the selected time period.")
except Exception as e:
    st.error(f"Error retrieving data: {str(e)}")
    data = pd.DataFrame()

if not data.empty:
    with analysis_tabs[0]:  # Multi-Indicator Analysis
        st.subheader("Multi-Indicator Analysis")
        
        # Indicator selection
        col1, col2, col3 = st.columns(3)
        
        with col1:
            show_sma = st.checkbox("Show SMA", value=True)
            sma_period = st.slider("SMA Period", min_value=5, max_value=200, value=20, step=1)
            
            show_ema = st.checkbox("Show EMA", value=True)
            ema_period = st.slider("EMA Period", min_value=5, max_value=200, value=20, step=1)
        
        with col2:
            show_bollinger = st.checkbox("Show Bollinger Bands", value=True)
            bb_period = st.slider("Bollinger Period", min_value=5, max_value=50, value=20, step=1)
            bb_std = st.slider("Bollinger StdDev", min_value=1, max_value=4, value=2, step=1)
            
            show_volume = st.checkbox("Show Volume", value=True)
        
        with col3:
            show_rsi = st.checkbox("Show RSI", value=True)
            rsi_period = st.slider("RSI Period", min_value=5, max_value=50, value=14, step=1)
            
            show_macd = st.checkbox("Show MACD", value=True)
        
        # Calculate indicators
        if show_sma:
            data['SMA'] = calculate_sma(data['price'], sma_period)
        
        if show_ema:
            data['EMA'] = calculate_ema(data['price'], ema_period)
        
        if show_bollinger:
            middle_band, upper_band, lower_band = calculate_bollinger_bands(data['price'], bb_period, bb_std)
            data['BB_Middle'] = middle_band
            data['BB_Upper'] = upper_band
            data['BB_Lower'] = lower_band
        
        if show_rsi:
            data['RSI'] = calculate_rsi(data['price'], rsi_period)
        
        if show_macd:
            macd_line, signal_line, histogram = calculate_macd(data['price'])
            data['MACD'] = macd_line
            data['MACD_Signal'] = signal_line
            data['MACD_Histogram'] = histogram
        
        # Create subplots
        fig = go.Figure()
        
        # Define subplot structure based on indicators
        subplot_rows = 1
        if show_rsi or show_macd:
            subplot_rows += 1
        if show_macd and show_rsi:
            subplot_rows += 1
        
        fig = make_subplots(rows=subplot_rows, cols=1, 
                          shared_xaxes=True, 
                          vertical_spacing=0.05,
                          row_heights=[0.6] + [0.2] * (subplot_rows - 1))
        
        # Main price plot
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data['price'],
                mode='lines',
                name='Price',
                line=dict(color='#0083B8', width=2),
                hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
            ),
            row=1, col=1
        )
        
        # Add indicators to the main plot
        if show_sma:
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['SMA'],
                    mode='lines',
                    name=f'SMA({sma_period})',
                    line=dict(color='#FF9900', width=1.5, dash='dot'),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                ),
                row=1, col=1
            )
        
        if show_ema:
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['EMA'],
                    mode='lines',
                    name=f'EMA({ema_period})',
                    line=dict(color='#9C27B0', width=1.5, dash='dot'),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                ),
                row=1, col=1
            )
        
        if show_bollinger:
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['BB_Upper'],
                    mode='lines',
                    name='BB Upper',
                    line=dict(color='rgba(0,100,80,0.4)', width=1),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                ),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['BB_Middle'],
                    mode='lines',
                    name='BB Middle',
                    line=dict(color='rgba(0,100,80,0.7)', width=1),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                ),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['BB_Lower'],
                    mode='lines',
                    name='BB Lower',
                    fill='tonexty',
                    fillcolor='rgba(0,100,80,0.1)',
                    line=dict(color='rgba(0,100,80,0.4)', width=1),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                ),
                row=1, col=1
            )
        
        # Add RSI subplot
        current_row = 2
        if show_rsi:
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['RSI'],
                    mode='lines',
                    name=f'RSI({rsi_period})',
                    line=dict(color='#E91E63', width=1.5),
                    hovertemplate='%{x}<br>%{y:.2f}<extra></extra>'
                ),
                row=current_row, col=1
            )
            
            # Add RSI overbought/oversold lines
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[70, 70],
                    mode='lines',
                    name='Overbought (70)',
                    line=dict(color='rgba(233,30,99,0.3)', width=1, dash='dash'),
                    hoverinfo='skip'
                ),
                row=current_row, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[30, 30],
                    mode='lines',
                    name='Oversold (30)',
                    line=dict(color='rgba(233,30,99,0.3)', width=1, dash='dash'),
                    hoverinfo='skip'
                ),
                row=current_row, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[50, 50],
                    mode='lines',
                    name='Midline (50)',
                    line=dict(color='rgba(233,30,99,0.2)', width=1, dash='dash'),
                    hoverinfo='skip'
                ),
                row=current_row, col=1
            )
            
            current_row += 1
        
        # Add MACD subplot
        if show_macd:
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['MACD'],
                    mode='lines',
                    name='MACD',
                    line=dict(color='#2196F3', width=1.5),
                    hovertemplate='%{x}<br>%{y:.6f}<extra></extra>'
                ),
                row=current_row, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['MACD_Signal'],
                    mode='lines',
                    name='Signal',
                    line=dict(color='#FF5722', width=1.5),
                    hovertemplate='%{x}<br>%{y:.6f}<extra></extra>'
                ),
                row=current_row, col=1
            )
            
            # Add MACD histogram
            colors = ['#4CAF50' if val >= 0 else '#F44336' for val in data['MACD_Histogram']]
            
            fig.add_trace(
                go.Bar(
                    x=data.index,
                    y=data['MACD_Histogram'],
                    name='Histogram',
                    marker_color=colors,
                    opacity=0.7,
                    hovertemplate='%{x}<br>%{y:.6f}<extra></extra>'
                ),
                row=current_row, col=1
            )
            
            # Add zero line
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[0, 0],
                    mode='lines',
                    name='Zero Line',
                    line=dict(color='rgba(0,0,0,0.2)', width=1, dash='dash'),
                    hoverinfo='skip'
                ),
                row=current_row, col=1
            )
        
        # Update layout
        fig.update_layout(
            title=f'{selected_crypto} Price with Technical Indicators ({time_period})',
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            hovermode='x unified',
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            template='plotly_white',
            height=800
        )
        
        # Update y-axis for RSI
        if show_rsi:
            fig.update_yaxes(title_text="RSI", range=[0, 100], row=2, col=1)
        
        # Update y-axis for MACD
        macd_row = 3 if show_rsi else 2
        if show_macd:
            fig.update_yaxes(title_text="MACD", row=macd_row, col=1)
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Technical analysis summary
        st.subheader("Technical Analysis Summary")
        
        # Calculate latest indicator values
        latest_price = data['price'].iloc[-1]
        
        summary_data = []
        
        if show_sma:
            latest_sma = data['SMA'].iloc[-1]
            sma_signal = "Bullish" if latest_price > latest_sma else "Bearish"
            summary_data.append({
                "Indicator": f"SMA ({sma_period})",
                "Value": f"${latest_sma:.2f}",
                "Signal": sma_signal,
                "Analysis": f"Price is {'above' if latest_price > latest_sma else 'below'} SMA"
            })
        
        if show_ema:
            latest_ema = data['EMA'].iloc[-1]
            ema_signal = "Bullish" if latest_price > latest_ema else "Bearish"
            summary_data.append({
                "Indicator": f"EMA ({ema_period})",
                "Value": f"${latest_ema:.2f}",
                "Signal": ema_signal,
                "Analysis": f"Price is {'above' if latest_price > latest_ema else 'below'} EMA"
            })
        
        if show_bollinger:
            latest_upper = data['BB_Upper'].iloc[-1]
            latest_middle = data['BB_Middle'].iloc[-1]
            latest_lower = data['BB_Lower'].iloc[-1]
            
            if latest_price > latest_upper:
                bb_signal = "Overbought"
                bb_analysis = "Price is above upper band, suggesting potential reversal or continuation of strong uptrend"
            elif latest_price < latest_lower:
                bb_signal = "Oversold"
                bb_analysis = "Price is below lower band, suggesting potential reversal or continuation of strong downtrend"
            else:
                bb_signal = "Neutral"
                bb_analysis = f"Price is within bands, {'closer to upper band' if latest_price > latest_middle else 'closer to lower band'}"
            
            summary_data.append({
                "Indicator": f"Bollinger Bands ({bb_period}, {bb_std}σ)",
                "Value": f"Upper: ${latest_upper:.2f}, Middle: ${latest_middle:.2f}, Lower: ${latest_lower:.2f}",
                "Signal": bb_signal,
                "Analysis": bb_analysis
            })
        
        if show_rsi:
            latest_rsi = data['RSI'].iloc[-1]
            
            if latest_rsi > 70:
                rsi_signal = "Overbought"
                rsi_analysis = "RSI above 70 suggests potentially overbought conditions"
            elif latest_rsi < 30:
                rsi_signal = "Oversold"
                rsi_analysis = "RSI below 30 suggests potentially oversold conditions"
            else:
                rsi_signal = "Neutral"
                rsi_analysis = f"RSI at {latest_rsi:.2f}, suggesting {'bullish momentum' if latest_rsi > 50 else 'bearish momentum'}"
            
            summary_data.append({
                "Indicator": f"RSI ({rsi_period})",
                "Value": f"{latest_rsi:.2f}",
                "Signal": rsi_signal,
                "Analysis": rsi_analysis
            })
        
        if show_macd:
            latest_macd = data['MACD'].iloc[-1]
            latest_signal = data['MACD_Signal'].iloc[-1]
            latest_hist = data['MACD_Histogram'].iloc[-1]
            
            if latest_macd > latest_signal:
                macd_signal = "Bullish"
                if latest_hist > 0 and data['MACD_Histogram'].iloc[-2] <= 0:
                    macd_analysis = "MACD crossed above Signal line, suggesting bullish momentum"
                else:
                    macd_analysis = "MACD above Signal line, suggesting bullish momentum continues"
            else:
                macd_signal = "Bearish"
                if latest_hist < 0 and data['MACD_Histogram'].iloc[-2] >= 0:
                    macd_analysis = "MACD crossed below Signal line, suggesting bearish momentum"
                else:
                    macd_analysis = "MACD below Signal line, suggesting bearish momentum continues"
            
            summary_data.append({
                "Indicator": "MACD (12, 26, 9)",
                "Value": f"MACD: {latest_macd:.6f}, Signal: {latest_signal:.6f}, Hist: {latest_hist:.6f}",
                "Signal": macd_signal,
                "Analysis": macd_analysis
            })
        
        # Display summary
        summary_df = pd.DataFrame(summary_data)
        st.dataframe(summary_df, use_container_width=True)
        
        # Overall market sentiment
        bullish_count = sum(1 for item in summary_data if item["Signal"] in ["Bullish", "Oversold"])
        bearish_count = sum(1 for item in summary_data if item["Signal"] in ["Bearish", "Overbought"])
        neutral_count = len(summary_data) - bullish_count - bearish_count
        
        sentiment = "Bullish" if bullish_count > bearish_count else "Bearish" if bearish_count > bullish_count else "Neutral"
        sentiment_score = bullish_count - bearish_count
        
        st.subheader("Overall Technical Sentiment")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "Technical Sentiment", 
                sentiment,
                f"{sentiment_score:+d}"
            )
        
        with col2:
            st.metric(
                "Bullish Indicators", 
                bullish_count,
                f"{bullish_count}/{len(summary_data)}"
            )
        
        with col3:
            st.metric(
                "Bearish Indicators", 
                bearish_count,
                f"{bearish_count}/{len(summary_data)}"
            )

    with analysis_tabs[1]:  # Fibonacci & Support/Resistance
        st.subheader("Fibonacci Retracement & Support/Resistance Levels")
        
        # Calculate high and low for the period
        period_high = data['price'].max()
        period_low = data['price'].min()
        period_close = data['price'].iloc[-1]
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric(
                "Period High", 
                f"${period_high:.2f}",
                f"+{((period_high/period_low) - 1)*100:.2f}% from low"
            )
        
        with col2:
            st.metric(
                "Period Low", 
                f"${period_low:.2f}",
                f"-{((period_high/period_low) - 1)*100:.2f}% from high"
            )
        
        # Calculate Fibonacci retracement levels
        fib_levels = calculate_fibonacci_retracement(period_high, period_low)
        
        # Calculate pivot points
        pivot_levels = calculate_pivots(
            data['price'].iloc[-2],  # Yesterday's high
            data['price'].iloc[-2],  # Yesterday's low
            data['price'].iloc[-2]   # Yesterday's close
        )
        
        # Plot price with Fibonacci levels
        fig = go.Figure()
        
        # Add price line
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data['price'],
                mode='lines',
                name='Price',
                line=dict(color='#0083B8', width=2),
                hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
            )
        )
        
        # Add Fibonacci levels
        for level, price in fib_levels.items():
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[price, price],
                    mode='lines',
                    name=f'Fib {level:.3f}',
                    line=dict(color='rgba(233,30,99,0.7)', width=1, dash='dash'),
                    hovertemplate=f'Fibonacci {level:.3f}: ${price:,.2f}<extra></extra>'
                )
            )
        
        # Add pivot levels
        pivot_colors = {
            'pivot': 'rgba(33,150,243,0.7)',
            's1': 'rgba(139,195,74,0.7)',
            's2': 'rgba(139,195,74,0.7)',
            's3': 'rgba(139,195,74,0.7)',
            'r1': 'rgba(244,67,54,0.7)',
            'r2': 'rgba(244,67,54,0.7)',
            'r3': 'rgba(244,67,54,0.7)'
        }
        
        pivot_names = {
            'pivot': 'Pivot',
            's1': 'Support 1',
            's2': 'Support 2',
            's3': 'Support 3',
            'r1': 'Resistance 1',
            'r2': 'Resistance 2',
            'r3': 'Resistance 3'
        }
        
        for level, price in pivot_levels.items():
            fig.add_trace(
                go.Scatter(
                    x=[data.index[0], data.index[-1]],
                    y=[price, price],
                    mode='lines',
                    name=pivot_names[level],
                    line=dict(color=pivot_colors[level], width=1, dash='dot'),
                    hovertemplate=f'{pivot_names[level]}: ${price:,.2f}<extra></extra>'
                )
            )
        
        # Update layout
        fig.update_layout(
            title=f'{selected_crypto} Price with Fibonacci Retracement & Pivot Points',
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            hovermode='x unified',
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            template='plotly_white',
            height=600
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Display Fibonacci levels in a table
        st.subheader("Fibonacci Retracement Levels")
        
        fib_data = []
        for level, price in fib_levels.items():
            distance = ((price / period_close) - 1) * 100
            fib_data.append({
                "Level": f"{level:.3f}",
                "Price": f"${price:.2f}",
                "Distance": f"{distance:+.2f}%",
                "Notes": "High" if level == 1.0 else "Low" if level == 0.0 else ""
            })
        
        fib_df = pd.DataFrame(fib_data)
        st.dataframe(fib_df, use_container_width=True)
        
        # Display Pivot levels in a table
        st.subheader("Pivot Points (Classic)")
        
        pivot_data = []
        for level, price in pivot_levels.items():
            distance = ((price / period_close) - 1) * 100
            pivot_data.append({
                "Level": pivot_names[level],
                "Price": f"${price:.2f}",
                "Distance": f"{distance:+.2f}%"
            })
        
        pivot_df = pd.DataFrame(pivot_data)
        st.dataframe(pivot_df, use_container_width=True)
        
        # Key support and resistance identification
        st.subheader("Key Support & Resistance Areas")
        
        # This is a simplified approach; in a real application, you might want
        # to implement a more sophisticated algorithm to identify these levels
        
        # For demo purposes, we'll just highlight the Fibonacci and pivot levels
        # near the current price
        
        # Find nearby support levels
        support_levels = []
        for level, price in fib_levels.items():
            if price < period_close and ((period_close / price) - 1) * 100 < 10:
                support_levels.append((f"Fibonacci {level:.3f}", price))
                
        for level, price in pivot_levels.items():
            if level.startswith('s') and price < period_close and ((period_close / price) - 1) * 100 < 10:
                support_levels.append((pivot_names[level], price))
        
        support_levels.sort(key=lambda x: x[1], reverse=True)
        
        # Find nearby resistance levels
        resistance_levels = []
        for level, price in fib_levels.items():
            if price > period_close and ((price / period_close) - 1) * 100 < 10:
                resistance_levels.append((f"Fibonacci {level:.3f}", price))
                
        for level, price in pivot_levels.items():
            if level.startswith('r') and price > period_close and ((price / period_close) - 1) * 100 < 10:
                resistance_levels.append((pivot_names[level], price))
        
        resistance_levels.sort(key=lambda x: x[1])
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("Nearby Support Levels")
            if support_levels:
                for name, price in support_levels:
                    distance = ((period_close / price) - 1) * 100
                    st.write(f"• {name}: ${price:.2f} ({distance:.2f}% below)")
            else:
                st.write("No nearby support levels found")
        
        with col2:
            st.write("Nearby Resistance Levels")
            if resistance_levels:
                for name, price in resistance_levels:
                    distance = ((price / period_close) - 1) * 100
                    st.write(f"• {name}: ${price:.2f} ({distance:.2f}% above)")
            else:
                st.write("No nearby resistance levels found")

    with analysis_tabs[2]:  # Correlation Analysis
        st.subheader("Cryptocurrency Correlation Analysis")
        
        # Get data for all cryptocurrencies for comparison
        crypto_ids = ['bitcoin', 'ethereum', 'ripple', 'tether']
        
        all_crypto_data = {}
        for crypto_id in crypto_ids:
            try:
                crypto_data = get_crypto_data(crypto_id, start_date, end_date)
                if not crypto_data.empty:
                    all_crypto_data[crypto_id] = crypto_data['price']
            except Exception as e:
                st.error(f"Error retrieving data for {crypto_id}: {str(e)}")
        
        if len(all_crypto_data) > 1:
            # Create a DataFrame with all crypto prices
            correlation_df = pd.DataFrame(all_crypto_data)
            
            # Rename columns for display
            correlation_df = correlation_df.rename(columns={
                'bitcoin': 'Bitcoin',
                'ethereum': 'Ethereum',
                'ripple': 'XRP',
                'tether': 'Tether'
            })
            
            # Calculate correlations
            correlation_matrix = correlation_df.corr()
            
            # Plot correlation heatmap
            fig = go.Figure(data=go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.index,
                colorscale='Viridis',
                zmin=-1, zmax=1,
                hoverongaps=False,
                text=[[f'{val:.4f}' for val in row] for row in correlation_matrix.values],
                texttemplate="%{text}",
                colorbar=dict(title='Correlation')
            ))
            
            fig.update_layout(
                title=f'Cryptocurrency Price Correlation ({time_period})',
                height=500,
                template='plotly_white'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Correlation with selected crypto
            selected_crypto_corr = correlation_matrix[crypto_mapping[selected_crypto].replace('ripple', 'XRP')].drop(crypto_mapping[selected_crypto].replace('ripple', 'XRP'))
            
            st.subheader(f"Correlation with {selected_crypto}")
            
            corr_data = []
            for crypto, corr in selected_crypto_corr.items():
                corr_data.append({
                    "Cryptocurrency": crypto,
                    "Correlation": f"{corr:.4f}",
                    "Strength": "Strong Positive" if corr > 0.7 else
                               "Moderate Positive" if corr > 0.3 else
                               "Weak Positive" if corr > 0 else
                               "Weak Negative" if corr > -0.3 else
                               "Moderate Negative" if corr > -0.7 else
                               "Strong Negative"
                })
            
            corr_df = pd.DataFrame(corr_data)
            st.dataframe(corr_df, use_container_width=True)
            
            # Plot price comparison
            st.subheader("Price Comparison (Normalized)")
            
            # Normalize prices
            normalized_df = correlation_df.div(correlation_df.iloc[0]) * 100
            
            # Plot normalized prices
            fig = go.Figure()
            
            for col in normalized_df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=normalized_df.index,
                        y=normalized_df[col],
                        mode='lines',
                        name=col,
                        hovertemplate='%{x}<br>%{y:.2f}%<extra></extra>'
                    )
                )
            
            fig.update_layout(
                title='Normalized Price Comparison (Starting Price = 100%)',
                xaxis_title='Date',
                yaxis_title='Normalized Price (%)',
                hovermode='x unified',
                template='plotly_white',
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Interpretation
            st.subheader("Correlation Interpretation")
            
            st.write("""
            **What does correlation mean?**
            
            - Correlation of 1.0 means perfect positive correlation (prices move in the same direction)
            - Correlation of -1.0 means perfect negative correlation (prices move in opposite directions)
            - Correlation near 0 means no significant correlation
            
            **What can we learn?**
            
            - High positive correlation suggests cryptocurrencies respond similarly to market conditions
            - Low correlation suggests unique factors affecting price movements
            - Correlation can change over different time periods
            """)
            
            # Add some correlation insights specific to the selected crypto
            st.write(f"**{selected_crypto} Correlation Insights:**")
            
            highest_corr = selected_crypto_corr.idxmax()
            lowest_corr = selected_crypto_corr.idxmin()
            
            st.write(f"• {selected_crypto} has the strongest correlation with {highest_corr} ({selected_crypto_corr[highest_corr]:.4f})")
            st.write(f"• {selected_crypto} has the weakest correlation with {lowest_corr} ({selected_crypto_corr[lowest_corr]:.4f})")
            
        else:
            st.warning("Correlation analysis requires data for multiple cryptocurrencies. Some data could not be retrieved.")

    with analysis_tabs[3]:  # Volume Profile
        st.subheader("Volume Profile Analysis")
        
        # This is a simplified volume profile implementation
        # In a real application, you might want to use more sophisticated methods
        
        if 'volume' in data.columns:
            # Create price bins
            min_price = data['price'].min()
            max_price = data['price'].max()
            price_range = max_price - min_price
            
            num_bins = 20
            bin_size = price_range / num_bins
            
            # Create bins
            bins = [min_price + i * bin_size for i in range(num_bins + 1)]
            bin_labels = [i for i in range(num_bins)]
            
            # Assign each price to a bin
            data['price_bin'] = pd.cut(data['price'], bins=bins, labels=bin_labels)
            
            # Calculate volume per bin
            volume_profile = data.groupby('price_bin')['volume'].sum()
            
            # Find the point of control (price level with highest volume)
            poc_bin = volume_profile.idxmax()
            poc_price = min_price + (poc_bin + 0.5) * bin_size
            
            # Create bin price labels
            bin_prices = [min_price + (i + 0.5) * bin_size for i in range(num_bins)]
            
            # Plot volume profile
            fig = go.Figure()
            
            # Add price line
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['price'],
                    mode='lines',
                    name='Price',
                    line=dict(color='#0083B8', width=2),
                    yaxis='y1',
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                )
            )
            
            # Add volume profile
            fig.add_trace(
                go.Bar(
                    x=volume_profile.values,
                    y=bin_prices,
                    orientation='h',
                    name='Volume Profile',
                    marker_color='rgba(0,131,184,0.3)',
                    yaxis='y1',
                    hovertemplate='Price: $%{y:,.2f}<br>Volume: %{x:,.0f}<extra></extra>'
                )
            )
            
            # Add point of control
            fig.add_trace(
                go.Scatter(
                    x=[0, volume_profile.max() * 1.1],
                    y=[poc_price, poc_price],
                    mode='lines',
                    name='Point of Control',
                    line=dict(color='red', width=2, dash='dash'),
                    yaxis='y1',
                    hovertemplate='Point of Control: $%{y:,.2f}<extra></extra>'
                )
            )
            
            # Update layout
            fig.update_layout(
                title=f'{selected_crypto} Price with Volume Profile ({time_period})',
                xaxis_title='Volume',
                yaxis_title='Price (USD)',
                hovermode='closest',
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                template='plotly_white',
                height=600,
                xaxis=dict(side='top'),
                yaxis=dict(side='right')
            )
            
            # Adjust the width of the volume profile
            max_volume = volume_profile.max()
            fig.update_xaxes(range=[0, max_volume * 1.1])
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Volume profile insights
            st.subheader("Volume Profile Insights")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "Point of Control", 
                    f"${poc_price:.2f}",
                    f"{((poc_price/data['price'].iloc[-1]) - 1)*100:+.2f}% from current"
                )
            
            with col2:
                # Value Area calculation (simplified)
                # Typically this would be the price range containing 70% of the volume
                total_volume = volume_profile.sum()
                cumulative_volume = 0
                target_volume = total_volume * 0.7
                value_area_bins = []
                
                # Start from the POC and move outward
                for dist in range(num_bins):
                    lower_bin = max(0, poc_bin - dist)
                    upper_bin = min(num_bins - 1, poc_bin + dist)
                    
                    if lower_bin not in value_area_bins and lower_bin in volume_profile.index:
                        value_area_bins.append(lower_bin)
                        cumulative_volume += volume_profile.loc[lower_bin]
                    
                    if upper_bin not in value_area_bins and upper_bin in volume_profile.index:
                        value_area_bins.append(upper_bin)
                        cumulative_volume += volume_profile.loc[upper_bin]
                    
                    if cumulative_volume >= target_volume:
                        break
                
                value_area_low = min_price + min(value_area_bins) * bin_size
                value_area_high = min_price + (max(value_area_bins) + 1) * bin_size
                
                st.metric(
                    "Value Area Range", 
                    f"${value_area_low:.2f} - ${value_area_high:.2f}",
                    f"Width: ${value_area_high - value_area_low:.2f}"
                )
            
            with col3:
                # Liquidity analysis (simplified)
                high_volume_price = bin_prices[volume_profile.nlargest(3).index[1]]  # Second highest volume price
                low_volume_price = bin_prices[volume_profile.nsmallest(3).index[1]]  # Second lowest volume price
                
                st.metric(
                    "High/Low Volume Areas", 
                    f"${high_volume_price:.2f} / ${low_volume_price:.2f}",
                    "Secondary liquidity zones"
                )
            
            # Volume profile interpretation
            st.write("""
            **Volume Profile Analysis:**
            
            - **Point of Control (POC)** represents the price level with the highest trading volume, indicating strong interest from market participants
            - **Value Area** contains approximately 70% of the volume, showing where most trading activity has occurred
            - **High Volume Areas** suggest price levels with strong support or resistance due to significant trading interest
            - **Low Volume Areas** may indicate potential price gaps, where the price might move quickly through when tested
            
            This analysis helps identify key price levels where the market has shown significant interest, which often act as support or resistance in future trading.
            """)
        else:
            st.warning("Volume data is required for Volume Profile analysis but is not available.")