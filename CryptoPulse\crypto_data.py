import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os

# CoinGecko API endpoints
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"

# Map of cryptocurrency names to their CoinGecko IDs
CRYPTO_ID_MAP = {
    'bitcoin': 'bitcoin',
    'ethereum': 'ethereum',
    'ripple': 'ripple',
    'xrp': 'ripple',
    'tether': 'tether'
}

# Cache for API responses to avoid hitting rate limits
cache = {}
cache_expiry = {}
CACHE_DURATION = 60  # seconds

def get_with_cache(url, params=None):
    """Get data from the API with caching to avoid rate limits."""
    cache_key = f"{url}?{str(params)}"
    
    # If cached data exists and is still valid, return it
    if cache_key in cache and cache_expiry.get(cache_key, 0) > time.time():
        return cache[cache_key]
    
    # Otherwise, make a new request
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        # Cache the response
        cache[cache_key] = response.json()
        cache_expiry[cache_key] = time.time() + CACHE_DURATION
        
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request error: {e}")
        return None

def get_crypto_data(crypto_id, start_date, end_date):
    """
    Get historical price data for a cryptocurrency.
    
    Args:
        crypto_id (str): The ID of the cryptocurrency (e.g., 'bitcoin').
        start_date (datetime): Start date for the data.
        end_date (datetime): End date for the data.
        
    Returns:
        pd.DataFrame: DataFrame with date and price data.
    """
    # Convert ID if needed
    if crypto_id.lower() in CRYPTO_ID_MAP:
        crypto_id = CRYPTO_ID_MAP[crypto_id.lower()]
    
    # Convert dates to UNIX timestamps (milliseconds)
    start_timestamp = int(start_date.timestamp())
    end_timestamp = int(end_date.timestamp())
    
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/{crypto_id}/market_chart/range"
    params = {
        'vs_currency': 'usd',
        'from': start_timestamp,
        'to': end_timestamp
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    if not response_data or 'prices' not in response_data:
        return pd.DataFrame()
    
    # Convert to DataFrame
    prices = response_data['prices']
    df = pd.DataFrame(prices, columns=['timestamp', 'price'])
    
    # Convert timestamp to datetime (timestamps are in milliseconds)
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    
    return df

def get_current_prices(crypto_ids):
    """
    Get current prices and 24h changes for cryptocurrencies.
    
    Args:
        crypto_ids (list): List of cryptocurrency IDs.
        
    Returns:
        dict: Dictionary with current prices and 24h changes.
    """
    # Make sure IDs are mapped correctly
    crypto_ids = [CRYPTO_ID_MAP.get(crypto_id.lower(), crypto_id.lower()) for crypto_id in crypto_ids]
    
    # Convert list to comma-separated string
    ids_string = ','.join(crypto_ids)
    
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/markets"
    params = {
        'vs_currency': 'usd',
        'ids': ids_string,
        'order': 'market_cap_desc',
        'per_page': 100,
        'page': 1,
        'sparkline': False,
        'price_change_percentage': '24h'
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    if not response_data:
        return None
    
    # Create a dictionary with the current prices and price changes
    result = {}
    for crypto in response_data:
        result[crypto['id']] = {
            'current_price': crypto['current_price'],
            'price_change_24h': crypto['price_change_percentage_24h'] if crypto['price_change_percentage_24h'] else 0
        }
    
    return result

def get_crypto_details(crypto_id):
    """
    Get detailed information about a cryptocurrency.
    
    Args:
        crypto_id (str): The ID of the cryptocurrency.
        
    Returns:
        dict: Detailed information about the cryptocurrency.
    """
    # Convert ID if needed
    if crypto_id.lower() in CRYPTO_ID_MAP:
        crypto_id = CRYPTO_ID_MAP[crypto_id.lower()]
    
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/{crypto_id}"
    params = {
        'localization': 'false',
        'tickers': 'false',
        'market_data': 'true',
        'community_data': 'false',
        'developer_data': 'false',
        'sparkline': 'false'
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    if not response_data:
        return None
    
    return response_data

def get_top_cryptocurrencies(limit=100):
    """
    Get a list of top cryptocurrencies by market cap.
    
    Args:
        limit (int): The number of cryptocurrencies to return.
        
    Returns:
        pd.DataFrame: DataFrame with top cryptocurrencies.
    """
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/markets"
    params = {
        'vs_currency': 'usd',
        'order': 'market_cap_desc',
        'per_page': limit,
        'page': 1,
        'sparkline': False
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    if not response_data:
        return pd.DataFrame()
    
    # Convert to DataFrame
    df = pd.DataFrame(response_data)
    
    # Select relevant columns
    columns = [
        'id', 'symbol', 'name', 'current_price', 'market_cap', 
        'market_cap_rank', 'total_volume', 'price_change_percentage_24h'
    ]
    
    # Make sure all required columns exist
    available_columns = [col for col in columns if col in df.columns]
    
    return df[available_columns]
