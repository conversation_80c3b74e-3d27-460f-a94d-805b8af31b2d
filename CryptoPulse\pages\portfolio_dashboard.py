import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta

from crypto_data import get_current_prices
from portfolio import calculate_portfolio_value, calculate_portfolio_allocation, calculate_portfolio_performance, add_transaction, get_historical_portfolio_value
from utils import format_currency, format_percentage, get_crypto_icon

# Page title
st.title("Portfolio Dashboard")

# Get current prices for cryptocurrencies
try:
    current_prices = get_current_prices(['bitcoin', 'ethereum', 'ripple', 'tether'])
    
    if not current_prices:
        st.warning("Unable to fetch current prices. Portfolio values may not be accurate.")
        current_prices = {}
except Exception as e:
    st.error(f"Error retrieving price data: {str(e)}")
    current_prices = {}

# Create tabs for different portfolio views
portfolio_tabs = st.tabs(["Dashboard", "Manage Portfolio", "Performance", "Transactions"])

with portfolio_tabs[0]:  # Dashboard
    # Display portfolio summary
    st.subheader("Portfolio Summary")
    
    # Calculate portfolio value and metrics
    portfolio_value = calculate_portfolio_value(st.session_state.portfolio, current_prices)
    portfolio_allocation = calculate_portfolio_allocation(st.session_state.portfolio, current_prices)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Portfolio Value", f"${portfolio_value:,.2f}")
    
    with col2:
        # Number of assets held
        assets_held = sum(1 for crypto, data in st.session_state.portfolio.items() if data['amount'] > 0)
        st.metric("Assets Held", assets_held)
    
    with col3:
        # Most valuable holding
        if portfolio_allocation:
            max_allocation = max(portfolio_allocation.items(), key=lambda x: x[1])
            st.metric("Largest Holding", f"{max_allocation[0]} ({max_allocation[1]:.2f}%)")
        else:
            st.metric("Largest Holding", "None")
    
    # Portfolio allocation pie chart
    st.subheader("Asset Allocation")
    
    # Check if there are any assets in the portfolio
    has_assets = False
    for crypto, data in st.session_state.portfolio.items():
        if data['amount'] > 0:
            has_assets = True
            break
    
    if has_assets:
        # Create data for pie chart
        allocation_data = []
        
        for crypto, percentage in portfolio_allocation.items():
            if percentage > 0:
                crypto_id = crypto.lower()
                if crypto_id == 'xrp':
                    crypto_id = 'ripple'
                
                current_price = current_prices.get(crypto_id, {}).get('current_price', 0)
                amount = st.session_state.portfolio[crypto]['amount']
                value = amount * current_price
                
                allocation_data.append({
                    'Cryptocurrency': crypto,
                    'Allocation (%)': percentage,
                    'Value (USD)': value
                })
        
        if allocation_data:
            fig = px.pie(
                allocation_data, 
                values='Allocation (%)', 
                names='Cryptocurrency', 
                title='Portfolio Allocation',
                hover_data=['Value (USD)'],
                color_discrete_sequence=px.colors.qualitative.Safe
            )
            
            fig.update_traces(
                textposition='inside', 
                textinfo='percent+label',
                hovertemplate='<b>%{label}</b><br>Allocation: %{value:.2f}%<br>Value: $%{customdata[0]:,.2f}<extra></extra>'
            )
            
            fig.update_layout(
                height=400,
                margin=dict(t=40, b=0, l=0, r=0)
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No assets to display in allocation chart.")
    else:
        st.info("Your portfolio is empty. Add some assets to see the allocation chart.")
        st.image("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5", use_container_width=True)
    
    # Holdings breakdown table
    st.subheader("Holdings Breakdown")
    
    holdings_data = []
    
    for crypto, data in st.session_state.portfolio.items():
        if data['amount'] > 0:
            crypto_id = crypto.lower()
            if crypto_id == 'xrp':
                crypto_id = 'ripple'
            
            if crypto_id in current_prices:
                current_price = current_prices[crypto_id]['current_price']
                price_change_24h = current_prices[crypto_id]['price_change_24h']
                
                value = data['amount'] * current_price
                avg_price = data['avg_buy_price']
                profit_loss = (current_price - avg_price) * data['amount']
                profit_loss_percentage = ((current_price - avg_price) / avg_price) * 100 if avg_price > 0 else 0
                
                holdings_data.append({
                    'Cryptocurrency': f"{get_crypto_icon(crypto)} {crypto}",
                    'Amount': data['amount'],
                    'Current Price': f"${current_price:,.2f}",
                    'Value': f"${value:,.2f}",
                    'Avg Buy Price': f"${avg_price:,.2f}",
                    'Profit/Loss': f"${profit_loss:,.2f}",
                    'P/L %': f"{profit_loss_percentage:+.2f}%",
                    '24h Change': f"{price_change_24h:+.2f}%"
                })
    
    if holdings_data:
        holdings_df = pd.DataFrame(holdings_data)
        st.dataframe(holdings_df, use_container_width=True)
    else:
        st.info("Your portfolio is empty. Add some assets to see them here.")
    
    # Historical portfolio value chart
    st.subheader("Portfolio Performance")
    
    # Time period selector for historical chart
    time_period = st.radio(
        "Time period:",
        ["1 Week", "1 Month", "3 Months"],
        horizontal=True
    )
    
    # Map time period to days
    time_mapping = {
        "1 Week": 7,
        "1 Month": 30,
        "3 Months": 90
    }
    
    days = time_mapping[time_period]
    
    if has_assets:
        try:
            # Get historical portfolio value
            historical_value = get_historical_portfolio_value(st.session_state.portfolio, days)
            
            if not historical_value.empty:
                fig = go.Figure()
                
                # Add total portfolio value line
                fig.add_trace(
                    go.Scatter(
                        x=historical_value.index,
                        y=historical_value['total'],
                        mode='lines',
                        name='Total Portfolio Value',
                        line=dict(color='#0083B8', width=3),
                        hovertemplate='%{x}<br>$%{y:,.2f}<extra></extra>'
                    )
                )
                
                # Add individual asset lines
                for crypto in st.session_state.portfolio:
                    if crypto in historical_value.columns and crypto != 'total':
                        fig.add_trace(
                            go.Scatter(
                                x=historical_value.index,
                                y=historical_value[crypto],
                                mode='lines',
                                name=crypto,
                                line=dict(width=1.5),
                                hovertemplate='%{x}<br>$%{y:,.2f}<extra></extra>'
                            )
                        )
                
                fig.update_layout(
                    title='Historical Portfolio Value',
                    xaxis_title='Date',
                    yaxis_title='Value (USD)',
                    hovermode='x unified',
                    template='plotly_white',
                    height=500,
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )
                
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No historical data available for your portfolio.")
                
        except Exception as e:
            st.error(f"Error retrieving historical data: {str(e)}")
            st.image("https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a", use_container_width=True)
    else:
        st.info("Add assets to your portfolio to see historical performance.")
        st.image("https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a", use_container_width=True)

with portfolio_tabs[1]:  # Manage Portfolio
    st.subheader("Add or Update Holdings")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Form for adding assets
        with st.form("add_asset_form"):
            st.write("Add cryptocurrency to your portfolio")
            
            crypto = st.selectbox(
                "Select Cryptocurrency",
                ["Bitcoin", "Ethereum", "XRP", "Tether"]
            )
            
            transaction_type = st.radio(
                "Transaction Type",
                ["Buy", "Sell"],
                horizontal=True
            )
            
            amount = st.number_input("Amount", min_value=0.0, step=0.001, format="%.6f")
            price = st.number_input("Price per coin (USD)", min_value=0.0, step=0.01, format="%.2f")
            
            submit_button = st.form_submit_button("Add Transaction")
            
            if submit_button:
                if amount <= 0:
                    st.error("Amount must be greater than zero.")
                elif price <= 0:
                    st.error("Price must be greater than zero.")
                else:
                    # Adjust amount based on transaction type
                    if transaction_type == "Sell":
                        amount = -amount
                    
                    # Add transaction to portfolio
                    st.session_state.portfolio = add_transaction(
                        st.session_state.portfolio,
                        crypto,
                        amount,
                        price
                    )
                    
                    st.success(f"{transaction_type} transaction for {abs(amount)} {crypto} at ${price:.2f} added successfully.")
                    st.rerun()
    
    with col2:
        st.image("https://images.unsplash.com/photo-1460925895917-afdab827c52f", use_container_width=True)
    
    # Current portfolio overview
    st.subheader("Current Portfolio")
    
    portfolio_data = []
    
    for crypto, data in st.session_state.portfolio.items():
        portfolio_data.append({
            'Cryptocurrency': crypto,
            'Amount': f"{data['amount']:.6f}",
            'Average Buy Price': f"${data['avg_buy_price']:.2f}"
        })
    
    if portfolio_data:
        portfolio_df = pd.DataFrame(portfolio_data)
        st.dataframe(portfolio_df, use_container_width=True)
    else:
        st.info("Your portfolio is empty. Add some assets to see them here.")
    
    # Reset portfolio button
    if st.button("Reset Portfolio"):
        st.session_state.portfolio = {
            'Bitcoin': {'amount': 0.0, 'avg_buy_price': 0.0},
            'Ethereum': {'amount': 0.0, 'avg_buy_price': 0.0},
            'XRP': {'amount': 0.0, 'avg_buy_price': 0.0},
            'Tether': {'amount': 0.0, 'avg_buy_price': 0.0}
        }
        st.success("Portfolio has been reset.")
        st.rerun()
    
    # Quick add sample portfolio for testing
    st.subheader("Quick Add Sample Portfolio")
    
    if st.button("Add Sample Portfolio"):
        st.session_state.portfolio = {
            'Bitcoin': {'amount': 0.25, 'avg_buy_price': 35000.0},
            'Ethereum': {'amount': 3.0, 'avg_buy_price': 1800.0},
            'XRP': {'amount': 1000.0, 'avg_buy_price': 0.5},
            'Tether': {'amount': 500.0, 'avg_buy_price': 1.0}
        }
        st.success("Sample portfolio has been added.")
        st.rerun()

with portfolio_tabs[2]:  # Performance
    st.subheader("Portfolio Performance Analysis")
    
    # Check if there are any assets in the portfolio
    has_assets = False
    for crypto, data in st.session_state.portfolio.items():
        if data['amount'] > 0:
            has_assets = True
            break
    
    if has_assets and current_prices:
        # Calculate portfolio performance
        portfolio_performance = calculate_portfolio_performance(st.session_state.portfolio, current_prices)
        
        if portfolio_performance:
            # Overall performance metrics
            total_perf = portfolio_performance['total']
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "Total Investment", 
                    f"${total_perf['cost']:,.2f}"
                )
            
            with col2:
                st.metric(
                    "Current Value", 
                    f"${total_perf['value']:,.2f}"
                )
            
            with col3:
                pl_color = "green" if total_perf['profit_loss'] >= 0 else "red"
                st.metric(
                    "Profit/Loss", 
                    f"${total_perf['profit_loss']:,.2f}",
                    f"{total_perf['profit_loss_percentage']:+.2f}%"
                )
            
            # Performance breakdown by asset
            st.subheader("Performance by Asset")
            
            performance_data = []
            
            for crypto, perf in portfolio_performance.items():
                if crypto != 'total' and st.session_state.portfolio[crypto]['amount'] > 0:
                    performance_data.append({
                        'Cryptocurrency': f"{get_crypto_icon(crypto)} {crypto}",
                        'Amount': st.session_state.portfolio[crypto]['amount'],
                        'Avg Buy Price': f"${perf['avg_buy_price']:,.2f}",
                        'Current Price': f"${perf['current_price']:,.2f}",
                        'Investment': f"${perf['cost']:,.2f}",
                        'Current Value': f"${perf['value']:,.2f}",
                        'Profit/Loss': f"${perf['profit_loss']:,.2f}",
                        'P/L %': f"{perf['profit_loss_percentage']:+.2f}%"
                    })
            
            if performance_data:
                perf_df = pd.DataFrame(performance_data)
                st.dataframe(perf_df, use_container_width=True)
            
            # Performance visualization
            st.subheader("Performance Visualization")
            
            # Bar chart comparing cost vs value for each asset
            cost_value_data = []
            
            for crypto, perf in portfolio_performance.items():
                if crypto != 'total' and st.session_state.portfolio[crypto]['amount'] > 0:
                    cost_value_data.append({
                        'Cryptocurrency': crypto,
                        'Investment': perf['cost'],
                        'Current Value': perf['value']
                    })
            
            if cost_value_data:
                cost_value_df = pd.DataFrame(cost_value_data)
                
                fig = go.Figure()
                
                fig.add_trace(
                    go.Bar(
                        x=cost_value_df['Cryptocurrency'],
                        y=cost_value_df['Investment'],
                        name='Investment',
                        marker_color='#1F77B4'
                    )
                )
                
                fig.add_trace(
                    go.Bar(
                        x=cost_value_df['Cryptocurrency'],
                        y=cost_value_df['Current Value'],
                        name='Current Value',
                        marker_color='#2CA02C'
                    )
                )
                
                fig.update_layout(
                    title='Investment vs Current Value by Asset',
                    xaxis_title='Cryptocurrency',
                    yaxis_title='USD',
                    barmode='group',
                    template='plotly_white',
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
            # Profit/Loss waterfall chart
            pl_data = []
            
            for crypto, perf in portfolio_performance.items():
                if crypto != 'total' and st.session_state.portfolio[crypto]['amount'] > 0:
                    pl_data.append({
                        'Cryptocurrency': crypto,
                        'Profit/Loss': perf['profit_loss']
                    })
            
            if pl_data:
                pl_df = pd.DataFrame(pl_data)
                
                fig = go.Figure()
                
                for i, row in pl_df.iterrows():
                    color = 'green' if row['Profit/Loss'] >= 0 else 'red'
                    
                    fig.add_trace(
                        go.Bar(
                            x=[row['Cryptocurrency']],
                            y=[row['Profit/Loss']],
                            name=row['Cryptocurrency'],
                            marker_color=color
                        )
                    )
                
                fig.update_layout(
                    title='Profit/Loss by Asset',
                    xaxis_title='Cryptocurrency',
                    yaxis_title='Profit/Loss (USD)',
                    template='plotly_white',
                    height=400,
                    showlegend=False
                )
                
                st.plotly_chart(fig, use_container_width=True)
            
        else:
            st.warning("Unable to calculate portfolio performance. Check your portfolio data.")
            st.image("https://images.unsplash.com/photo-1518186233392-c232efbf2373", use_container_width=True)
    else:
        if not has_assets:
            st.info("Your portfolio is empty. Add some assets to see performance analysis.")
        else:
            st.warning("Unable to retrieve current prices. Performance analysis is not available.")
        
        st.image("https://images.unsplash.com/photo-1518186233392-c232efbf2373", use_container_width=True)

with portfolio_tabs[3]:  # Transactions
    st.subheader("Transaction History")
    st.warning("Transaction history functionality is not yet implemented. This feature will be available in a future update.")
    
    st.markdown("""
    The Transaction History page will allow you to:
    
    - View all past transactions
    - Filter transactions by cryptocurrency
    - Sort by date, amount, or type
    - Export transaction history to CSV
    """)
    
    st.image("https://images.unsplash.com/photo-1542744173-05336fcc7ad4", use_container_width=True)
