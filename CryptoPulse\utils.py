import streamlit as st
import requests
from io import BytesIO
from PIL import Image
import base64

def format_currency(value, precision=2):
    """
    Format a value as a currency string.
    
    Args:
        value (float): The value to format.
        precision (int): Number of decimal places.
        
    Returns:
        str: Formatted currency string.
    """
    if value >= 1_000_000_000:
        return f"${value / 1_000_000_000:.{precision}f}B"
    elif value >= 1_000_000:
        return f"${value / 1_000_000:.{precision}f}M"
    elif value >= 1_000:
        return f"${value / 1_000:.{precision}f}K"
    else:
        return f"${value:.{precision}f}"

def format_percentage(value, precision=2):
    """
    Format a value as a percentage string.
    
    Args:
        value (float): The value to format.
        precision (int): Number of decimal places.
        
    Returns:
        str: Formatted percentage string.
    """
    return f"{value:.{precision}f}%"

def get_color_from_value(value):
    """
    Get a color based on whether a value is positive or negative.
    
    Args:
        value (float): The value to check.
        
    Returns:
        str: Color code.
    """
    return "green" if value >= 0 else "red"

def load_image(url):
    """
    Load an image from a URL.
    
    Args:
        url (str): URL of the image.
        
    Returns:
        PIL.Image: Loaded image.
    """
    try:
        response = requests.get(url)
        image = Image.open(BytesIO(response.content))
        return image
    except Exception as e:
        st.error(f"Error loading image: {str(e)}")
        return None

def get_crypto_icon(crypto_name):
    """
    Return emoji icon for a cryptocurrency.
    
    Args:
        crypto_name (str): Name of the cryptocurrency.
        
    Returns:
        str: Emoji representing the cryptocurrency.
    """
    icons = {
        'Bitcoin': '₿',
        'Ethereum': 'Ξ',
        'XRP': '✘',
        'Tether': '₮',
        'default': '🪙'
    }
    
    return icons.get(crypto_name, icons['default'])

def generate_stock_photo_urls():
    """
    Generate URLs for stock photos.
    
    Returns:
        dict: Dictionary with categorized stock photo URLs.
    """
    return {
        'cryptocurrency_charts': [
            "https://images.unsplash.com/photo-1639754390580-2e7437267698",
            "https://images.unsplash.com/photo-1639389016105-2fb11199fb6b",
            "https://images.unsplash.com/photo-1640592276475-56a1c277a38f",
            "https://images.unsplash.com/photo-1639987402632-d7273e921454",
            "https://images.unsplash.com/photo-1640143405373-bbb919afa0da",
            "https://images.unsplash.com/photo-1639768939489-025b90ba9f23"
        ],
        'portfolio_dashboard': [
            "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40",
            "https://images.unsplash.com/photo-1499951360447-b19be8fe80f5",
            "https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a",
            "https://images.unsplash.com/photo-1460925895917-afdab827c52f"
        ],
        'financial_analytics': [
            "https://images.unsplash.com/photo-1556155092-490a1ba16284",
            "https://images.unsplash.com/photo-1542744173-05336fcc7ad4",
            "https://images.unsplash.com/photo-1518186233392-c232efbf2373",
            "https://images.unsplash.com/photo-1559526324-4b87b5e36e44"
        ]
    }
