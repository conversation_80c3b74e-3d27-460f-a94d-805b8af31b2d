import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta

from crypto_data import get_crypto_data, get_current_prices, get_top_cryptocurrencies
from utils import format_currency, format_percentage, get_crypto_icon

# Page title
st.title("Market Overview")

# Time period selector
st.subheader("Select Time Period")
time_period = st.radio(
    "Time period:",
    ["1 Day", "1 Week", "1 Month", "3 Months", "1 Year"],
    horizontal=True
)

# Map time period to days
time_mapping = {
    "1 Day": 1,
    "1 Week": 7,
    "1 Month": 30,
    "3 Months": 90,
    "1 Year": 365
}

days = time_mapping[time_period]
end_date = datetime.now()
start_date = end_date - timedelta(days=days)

# Get current prices for the main cryptocurrencies
try:
    current_prices = get_current_prices(['bitcoin', 'ethereum', 'ripple', 'tether'])
    
    if current_prices:
        # Display current prices in a grid
        st.subheader("Current Prices")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            price_change = current_prices['bitcoin']['price_change_24h']
            st.metric(
                "Bitcoin (BTC)", 
                f"${current_prices['bitcoin']['current_price']:,.2f}",
                f"{price_change:.2f}%"
            )
        
        with col2:
            price_change = current_prices['ethereum']['price_change_24h']
            st.metric(
                "Ethereum (ETH)", 
                f"${current_prices['ethereum']['current_price']:,.2f}",
                f"{price_change:.2f}%"
            )
        
        with col3:
            price_change = current_prices['ripple']['price_change_24h']
            st.metric(
                "XRP", 
                f"${current_prices['ripple']['current_price']:,.4f}",
                f"{price_change:.2f}%"
            )
        
        with col4:
            price_change = current_prices['tether']['price_change_24h']
            st.metric(
                "Tether (USDT)", 
                f"${current_prices['tether']['current_price']:,.2f}",
                f"{price_change:.2f}%"
            )
    else:
        st.warning("Unable to fetch current prices. Please try again later.")
        
except Exception as e:
    st.error(f"Error retrieving price data: {str(e)}")

# Price chart
st.subheader("Price Comparison Chart")

# Create tabs for different chart views
chart_tabs = st.tabs(["Line Chart", "Candlestick", "Area Chart"])

# Get data for all four cryptocurrencies
cryptos = {
    'Bitcoin': 'bitcoin',
    'Ethereum': 'ethereum',
    'XRP': 'ripple',
    'Tether': 'tether'
}

# Store data in a dictionary
crypto_data = {}

for name, crypto_id in cryptos.items():
    try:
        data = get_crypto_data(crypto_id, start_date, end_date)
        if not data.empty:
            crypto_data[name] = data
        else:
            st.warning(f"No data available for {name} in the selected time period.")
    except Exception as e:
        st.error(f"Error retrieving {name} data: {str(e)}")

# Create visualization if we have data
if crypto_data:
    with chart_tabs[0]:  # Line Chart
        fig = go.Figure()
        
        for name, data in crypto_data.items():
            if name == 'Tether':  # Skip Tether in the main chart as its scale is very different
                continue
                
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['price'],
                    mode='lines',
                    name=name,
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                )
            )
        
        fig.update_layout(
            title='Cryptocurrency Price Comparison',
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            hovermode='x unified',
            template='plotly_white',
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Add a separate chart for Tether
        fig_tether = go.Figure()
        
        if 'Tether' in crypto_data:
            fig_tether.add_trace(
                go.Scatter(
                    x=crypto_data['Tether'].index,
                    y=crypto_data['Tether']['price'],
                    mode='lines',
                    name='Tether',
                    line=dict(color='#26A17B'),
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                )
            )
            
            fig_tether.update_layout(
                title='Tether Price (USDT)',
                xaxis_title='Date',
                yaxis_title='Price (USD)',
                template='plotly_white',
                height=300
            )
            
            st.plotly_chart(fig_tether, use_container_width=True)
    
    with chart_tabs[1]:  # Candlestick
        # We don't have OHLC data from our API function, so display a message
        st.info("Candlestick chart requires Open-High-Low-Close data, which is not available through the current API.")
        st.image("https://images.unsplash.com/photo-1640592276475-56a1c277a38f", use_container_width=True)
    
    with chart_tabs[2]:  # Area Chart
        fig = go.Figure()
        
        for name, data in crypto_data.items():
            if name == 'Tether':  # Skip Tether
                continue
                
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data['price'],
                    mode='lines',
                    name=name,
                    fill='tozeroy',
                    hovertemplate='%{x}<br>%{y:$,.2f}<extra></extra>'
                )
            )
        
        fig.update_layout(
            title='Cryptocurrency Price Area Chart',
            xaxis_title='Date',
            yaxis_title='Price (USD)',
            hovermode='x unified',
            template='plotly_white',
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
# Market cap and volume metrics
st.subheader("Market Metrics")

try:
    top_cryptos = get_top_cryptocurrencies(limit=20)
    
    if not top_cryptos.empty:
        # Filter to our focused cryptocurrencies
        focused_cryptos = top_cryptos[top_cryptos['id'].isin(['bitcoin', 'ethereum', 'ripple', 'tether'])]
        
        if not focused_cryptos.empty:
            # Select and rename columns
            display_cols = [
                'name', 'current_price', 'market_cap', 'total_volume', 
                'price_change_percentage_24h'
            ]
            
            # Make sure all columns exist
            available_cols = [col for col in display_cols if col in focused_cryptos.columns]
            
            # Create a new DataFrame with selected columns
            display_data = focused_cryptos[available_cols].copy()
            
            # Rename columns for display
            column_mapping = {
                'name': 'Name',
                'current_price': 'Price (USD)',
                'market_cap': 'Market Cap (USD)',
                'total_volume': 'Volume (24h)',
                'price_change_percentage_24h': 'Change (24h)'
            }
            
            # Only use mappings for columns that exist
            valid_mappings = {k: v for k, v in column_mapping.items() if k in available_cols}
            display_data = display_data.rename(columns=valid_mappings)
            
            # Format numeric columns
            if 'Price (USD)' in display_data.columns:
                display_data['Price (USD)'] = display_data['Price (USD)'].apply(
                    lambda x: f"${x:,.2f}" if x >= 0.01 else f"${x:,.4f}"
                )
            
            if 'Market Cap (USD)' in display_data.columns:
                display_data['Market Cap (USD)'] = display_data['Market Cap (USD)'].apply(
                    lambda x: format_currency(x, 2)
                )
            
            if 'Volume (24h)' in display_data.columns:
                display_data['Volume (24h)'] = display_data['Volume (24h)'].apply(
                    lambda x: format_currency(x, 2)
                )
            
            if 'Change (24h)' in display_data.columns:
                display_data['Change (24h)'] = display_data['Change (24h)'].apply(
                    lambda x: f"{x:+.2f}%" if x is not None else "N/A"
                )
            
            # Display the table
            st.dataframe(display_data, use_container_width=True)
            
        else:
            st.warning("No data available for the focused cryptocurrencies.")
    else:
        st.warning("No cryptocurrency market data available.")
        
except Exception as e:
    st.error(f"Error retrieving market data: {str(e)}")

# Market insights
st.subheader("Market Insights")

col1, col2 = st.columns(2)

with col1:
    st.image("https://images.unsplash.com/photo-1639389016105-2fb11199fb6b", use_container_width=True)
    st.markdown("""
    ### Bitcoin Dominance
    
    Bitcoin continues to be the dominant cryptocurrency in the market, with the largest market capitalization and trading volume. Its price movements often influence the entire cryptocurrency market.
    """)

with col2:
    st.image("https://images.unsplash.com/photo-1559526324-4b87b5e36e44", use_container_width=True)
    st.markdown("""
    ### Stablecoin Importance
    
    Tether (USDT) serves as an important stablecoin in the crypto ecosystem, providing stability and liquidity. Its price is pegged to the US dollar, making it less volatile compared to other cryptocurrencies.
    """)

# News and updates placeholder
st.subheader("Latest Market News")
st.info("This section would typically display recent news and updates about the cryptocurrency market from various sources.")

st.image("https://images.unsplash.com/photo-1639987402632-d7273e921454", use_container_width=True)
