import streamlit as st
import json
import os

# Define theme presets
THEMES = {
    "Default Blue": {
        "primaryColor": "#0083B8",
        "backgroundColor": "#FFFFFF",
        "secondaryBackgroundColor": "#F0F2F6",
        "textColor": "#262730",
        "font": "sans serif"
    },
    "Dark Crypto": {
        "primaryColor": "#F7931A",  # Bitcoin orange
        "backgroundColor": "#0E1117",
        "secondaryBackgroundColor": "#1B1F27",
        "textColor": "#FAFAFA",
        "font": "sans serif"
    },
    "Ethereum Theme": {
        "primaryColor": "#627EEA",  # Ethereum blue
        "backgroundColor": "#FFFFFF",
        "secondaryBackgroundColor": "#EAEAEA",
        "textColor": "#333333",
        "font": "sans serif"
    },
    "Trading View": {
        "primaryColor": "#2962FF",  # TradingView blue
        "backgroundColor": "#131722",
        "secondaryBackgroundColor": "#1E2230",
        "textColor": "#D9D9D9",
        "font": "sans serif"
    },
    "Green Candle": {
        "primaryColor": "#26A69A",  # Green candle color
        "backgroundColor": "#FFFFFF",
        "secondaryBackgroundColor": "#F5F5F5",
        "textColor": "#333333",
        "font": "sans serif"
    },
    "Binance": {
        "primaryColor": "#F0B90B",  # Binance yellow
        "backgroundColor": "#0C0E12",
        "secondaryBackgroundColor": "#1A1C24",
        "textColor": "#EAECEF",
        "font": "sans serif"
    }
}

def apply_theme(theme_name):
    """Apply the selected theme by writing to config.toml"""
    if theme_name not in THEMES:
        st.error(f"Theme '{theme_name}' not found.")
        return False
    
    theme = THEMES[theme_name]
    
    # Create .streamlit directory if it doesn't exist
    os.makedirs(".streamlit", exist_ok=True)
    
    # Read existing config to preserve other settings
    config_path = ".streamlit/config.toml"
    existing_config = {}
    
    # Initialize sections dictionary
    sections = {}
    
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            content = f.read()
            # Parse the existing TOML (simplified approach)
            current_section = None
            
            for line in content.split("\n"):
                line = line.strip()
                if not line:
                    continue
                    
                if line.startswith("[") and line.endswith("]"):
                    current_section = line[1:-1]
                    sections[current_section] = []
                elif current_section:
                    sections[current_section].append(line)
    
    # Create updated config content
    config_content = ""
    
    # Add server section if it exists
    if "server" in sections:
        config_content += "[server]\n"
        for line in sections["server"]:
            config_content += line + "\n"
        config_content += "\n"
    else:
        # Add default server settings
        config_content += "[server]\n"
        config_content += "headless = true\n"
        config_content += "address = \"0.0.0.0\"\n"
        config_content += "port = 5000\n\n"
    
    # Add theme section with new theme
    config_content += "[theme]\n"
    config_content += f"primaryColor = \"{theme['primaryColor']}\"\n"
    config_content += f"backgroundColor = \"{theme['backgroundColor']}\"\n"
    config_content += f"secondaryBackgroundColor = \"{theme['secondaryBackgroundColor']}\"\n"
    config_content += f"textColor = \"{theme['textColor']}\"\n"
    config_content += f"font = \"{theme['font']}\"\n"
    
    # Write updated config
    with open(config_path, "w") as f:
        f.write(config_content)
    
    return True

def get_theme_names():
    """Return a list of available theme names"""
    return list(THEMES.keys())

def get_current_theme():
    """Attempt to determine the current theme from config.toml"""
    config_path = ".streamlit/config.toml"
    if not os.path.exists(config_path):
        return "Default Blue"  # Default if no config exists
    
    with open(config_path, "r") as f:
        content = f.read()
    
    # Very simple parsing (not a full TOML parser)
    theme_section = False
    current_theme = {}
    
    for line in content.split("\n"):
        line = line.strip()
        if line == "[theme]":
            theme_section = True
            continue
        elif line.startswith("[") and line.endswith("]"):
            theme_section = False
            continue
        
        if theme_section and "=" in line:
            key, value = [x.strip() for x in line.split("=", 1)]
            value = value.strip('"')  # Remove quotes
            current_theme[key] = value
    
    # Compare with predefined themes
    for name, theme in THEMES.items():
        matches = True
        for key, value in theme.items():
            if key in current_theme and current_theme[key] != value:
                matches = False
                break
        if matches:
            return name
    
    return "Custom"  # If no match found

def add_theme_selector():
    """Add a theme selector widget to the sidebar"""
    with st.sidebar:
        st.markdown("---")
        st.subheader("Theme Settings")
        
        current_theme = get_current_theme()
        selected_theme = st.selectbox(
            "Select Theme:",
            get_theme_names(),
            index=get_theme_names().index(current_theme) if current_theme in get_theme_names() else 0
        )
        
        if st.button("Apply Theme"):
            success = apply_theme(selected_theme)
            if success:
                st.success(f"Theme '{selected_theme}' applied successfully! Please refresh the page.")
            else:
                st.error("Failed to apply theme.")