# CryptoTracker: API Documentation

## External API Integration

CryptoTracker relies on the CoinGecko API for cryptocurrency data. This document outlines the API endpoints used, request/response formats, and integration details.

## CoinGecko API

### Base URL
```
https://api.coingecko.com/api/v3
```

### Authentication
The application currently uses the free public API which has rate limits but doesn't require authentication. Future implementations may use the Pro API with API keys.

### Rate Limits
- Free tier: 10-50 calls/minute
- Our implementation uses caching to stay within these limits

## Endpoints Used

### 1. Get Coin Market Data

Retrieves current price, market cap, and 24h changes for specified cryptocurrencies.

**Endpoint:** `/coins/markets`

**HTTP Method:** GET

**Parameters:**
- `vs_currency`: Currency for price data (e.g., 'usd')
- `ids`: Comma-separated list of cryptocurrency IDs
- `order`: Sorting parameter (default: 'market_cap_desc')
- `per_page`: Number of results per page
- `page`: Page number
- `sparkline`: Whether to include sparkline data (default: false)
- `price_change_percentage`: Time periods for price change (e.g., '24h')

**Example Request:**
```
/coins/markets?vs_currency=usd&ids=bitcoin,ethereum,ripple,tether&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h
```

**Example Response:**
```json
[
  {
    "id": "bitcoin",
    "symbol": "btc",
    "name": "Bitcoin",
    "current_price": 45000.12,
    "market_cap": 850000000000,
    "market_cap_rank": 1,
    "total_volume": 28000000000,
    "price_change_24h": 2500.45,
    "price_change_percentage_24h": 5.8
    // Additional fields...
  },
  // Additional cryptocurrencies...
]
```

**Implementation in `crypto_data.py`:**
```python
def get_current_prices(crypto_ids):
    # Convert list to comma-separated string
    ids_string = ','.join(crypto_ids)
    
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/markets"
    params = {
        'vs_currency': 'usd',
        'ids': ids_string,
        'order': 'market_cap_desc',
        'per_page': 100,
        'page': 1,
        'sparkline': False,
        'price_change_percentage': '24h'
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    # Process response...
```

### 2. Get Historical Market Data

Retrieves historical price data for a specific cryptocurrency within a date range.

**Endpoint:** `/coins/{id}/market_chart/range`

**HTTP Method:** GET

**Parameters:**
- `vs_currency`: Currency for price data (e.g., 'usd')
- `from`: Start timestamp (Unix time in seconds)
- `to`: End timestamp (Unix time in seconds)

**Example Request:**
```
/coins/bitcoin/market_chart/range?vs_currency=usd&from=1612345600&to=1643881600
```

**Example Response:**
```json
{
  "prices": [
    [1612345600000, 33000.42],
    [1612432000000, 33500.13],
    // Additional timestamp-price pairs...
  ],
  "market_caps": [
    // Timestamp-market cap pairs...
  ],
  "total_volumes": [
    // Timestamp-volume pairs...
  ]
}
```

**Implementation in `crypto_data.py`:**
```python
def get_crypto_data(crypto_id, start_date, end_date):
    # Convert dates to UNIX timestamps (seconds)
    start_timestamp = int(start_date.timestamp())
    end_timestamp = int(end_date.timestamp())
    
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/{crypto_id}/market_chart/range"
    params = {
        'vs_currency': 'usd',
        'from': start_timestamp,
        'to': end_timestamp
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    # Process response...
```

### 3. Get Detailed Coin Data

Retrieves comprehensive information about a specific cryptocurrency.

**Endpoint:** `/coins/{id}`

**HTTP Method:** GET

**Parameters:**
- `localization`: Whether to include localized data (default: false)
- `tickers`: Whether to include ticker data (default: true)
- `market_data`: Whether to include market data (default: true)
- `community_data`: Whether to include community data (default: true)
- `developer_data`: Whether to include developer data (default: true)
- `sparkline`: Whether to include sparkline data (default: false)

**Example Request:**
```
/coins/ethereum?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false
```

**Example Response:**
```json
{
  "id": "ethereum",
  "symbol": "eth",
  "name": "Ethereum",
  "description": {
    "en": "Ethereum is a decentralized platform..."
  },
  "image": {
    "thumb": "https://...",
    "small": "https://...",
    "large": "https://..."
  },
  "market_data": {
    "current_price": {
      "usd": 3000.42
    },
    "market_cap": {
      "usd": 350000000000
    },
    // Additional market data...
  },
  // Additional fields...
}
```

**Implementation in `crypto_data.py`:**
```python
def get_crypto_details(crypto_id):
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/{crypto_id}"
    params = {
        'localization': 'false',
        'tickers': 'false',
        'market_data': 'true',
        'community_data': 'false',
        'developer_data': 'false',
        'sparkline': 'false'
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    # Process response...
```

### 4. Get Top Cryptocurrencies

Retrieves a list of top cryptocurrencies by market capitalization.

**Endpoint:** `/coins/markets`

**HTTP Method:** GET

**Parameters:**
- `vs_currency`: Currency for price data (e.g., 'usd')
- `order`: Sorting parameter (default: 'market_cap_desc')
- `per_page`: Number of results per page
- `page`: Page number
- `sparkline`: Whether to include sparkline data (default: false)

**Example Request:**
```
/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false
```

**Example Response:**
```json
[
  {
    "id": "bitcoin",
    "symbol": "btc",
    "name": "Bitcoin",
    "current_price": 45000.12,
    "market_cap": 850000000000,
    "market_cap_rank": 1,
    "total_volume": 28000000000,
    // Additional fields...
  },
  // Additional cryptocurrencies...
]
```

**Implementation in `crypto_data.py`:**
```python
def get_top_cryptocurrencies(limit=100):
    # Construct API URL
    url = f"{COINGECKO_API_URL}/coins/markets"
    params = {
        'vs_currency': 'usd',
        'order': 'market_cap_desc',
        'per_page': limit,
        'page': 1,
        'sparkline': False
    }
    
    # Get data from API
    response_data = get_with_cache(url, params)
    
    # Process response...
```

## Caching Implementation

To handle API rate limits, we implement a simple caching mechanism:

```python
# Cache for API responses to avoid hitting rate limits
cache = {}
cache_expiry = {}
CACHE_DURATION = 60  # seconds

def get_with_cache(url, params=None):
    """Get data from the API with caching to avoid rate limits."""
    cache_key = f"{url}?{str(params)}"
    
    # If cached data exists and is still valid, return it
    if cache_key in cache and cache_expiry.get(cache_key, 0) > time.time():
        return cache[cache_key]
    
    # Otherwise, make a new request
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        # Cache the response
        cache[cache_key] = response.json()
        cache_expiry[cache_key] = time.time() + CACHE_DURATION
        
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request error: {e}")
        return None
```

## Error Handling

API requests include error handling to manage:
- Network failures
- API rate limiting
- Invalid responses
- Authentication errors (for future implementations)

## Future API Enhancements

1. **API Key Authentication**: Implement authentication for CoinGecko Pro
2. **WebSocket Integration**: Real-time price updates
3. **Additional Endpoints**: Support for more data types
4. **Multiple Data Sources**: Integration with additional cryptocurrency APIs
5. **Persistent Caching**: Database-backed cache instead of in-memory