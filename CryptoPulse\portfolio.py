import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_data import get_crypto_data, get_current_prices

def calculate_portfolio_value(portfolio, current_prices):
    """
    Calculate the total value of the portfolio.
    
    Args:
        portfolio (dict): Portfolio data.
        current_prices (dict): Current prices of cryptocurrencies.
        
    Returns:
        float: Total portfolio value.
    """
    total_value = 0.0
    
    for crypto, data in portfolio.items():
        crypto_id = crypto.lower()
        if crypto_id == 'xrp':
            crypto_id = 'ripple'
            
        amount = data['amount']
        
        if amount > 0 and crypto_id in current_prices:
            price = current_prices[crypto_id]['current_price']
            total_value += amount * price
    
    return total_value

def calculate_portfolio_allocation(portfolio, current_prices):
    """
    Calculate the percentage allocation of each cryptocurrency in the portfolio.
    
    Args:
        portfolio (dict): Portfolio data.
        current_prices (dict): Current prices of cryptocurrencies.
        
    Returns:
        dict: Percentage allocation for each cryptocurrency.
    """
    total_value = calculate_portfolio_value(portfolio, current_prices)
    allocation = {}
    
    if total_value <= 0:
        return {crypto: 0 for crypto in portfolio}
    
    for crypto, data in portfolio.items():
        crypto_id = crypto.lower()
        if crypto_id == 'xrp':
            crypto_id = 'ripple'
            
        amount = data['amount']
        
        if amount > 0 and crypto_id in current_prices:
            price = current_prices[crypto_id]['current_price']
            value = amount * price
            allocation[crypto] = (value / total_value) * 100
        else:
            allocation[crypto] = 0
            
    return allocation

def calculate_portfolio_performance(portfolio, current_prices):
    """
    Calculate the performance of the portfolio.
    
    Args:
        portfolio (dict): Portfolio data.
        current_prices (dict): Current prices of cryptocurrencies.
        
    Returns:
        dict: Performance metrics for the portfolio.
    """
    total_cost = 0.0
    total_value = 0.0
    performance = {}
    
    for crypto, data in portfolio.items():
        crypto_id = crypto.lower()
        if crypto_id == 'xrp':
            crypto_id = 'ripple'
            
        amount = data['amount']
        avg_buy_price = data['avg_buy_price']
        
        if amount > 0 and crypto_id in current_prices:
            current_price = current_prices[crypto_id]['current_price']
            cost = amount * avg_buy_price
            value = amount * current_price
            
            profit_loss = value - cost
            profit_loss_percentage = (profit_loss / cost) * 100 if cost > 0 else 0
            
            performance[crypto] = {
                'amount': amount,
                'avg_buy_price': avg_buy_price,
                'current_price': current_price,
                'cost': cost,
                'value': value,
                'profit_loss': profit_loss,
                'profit_loss_percentage': profit_loss_percentage
            }
            
            total_cost += cost
            total_value += value
    
    # Calculate overall portfolio performance
    total_profit_loss = total_value - total_cost
    total_profit_loss_percentage = (total_profit_loss / total_cost) * 100 if total_cost > 0 else 0
    
    performance['total'] = {
        'cost': total_cost,
        'value': total_value,
        'profit_loss': total_profit_loss,
        'profit_loss_percentage': total_profit_loss_percentage
    }
    
    return performance

def add_transaction(portfolio, crypto, amount, price):
    """
    Add a new transaction to the portfolio.
    
    Args:
        portfolio (dict): Portfolio data.
        crypto (str): Cryptocurrency name.
        amount (float): Amount of cryptocurrency.
        price (float): Price per unit.
        
    Returns:
        dict: Updated portfolio.
    """
    if crypto not in portfolio:
        portfolio[crypto] = {'amount': 0.0, 'avg_buy_price': 0.0}
    
    if amount > 0:  # Buying
        current_amount = portfolio[crypto]['amount']
        current_avg_price = portfolio[crypto]['avg_buy_price']
        
        # Calculate new average buy price
        new_amount = current_amount + amount
        
        if new_amount > 0:
            new_avg_price = ((current_amount * current_avg_price) + (amount * price)) / new_amount
        else:
            new_avg_price = 0.0
            
        portfolio[crypto]['amount'] = new_amount
        portfolio[crypto]['avg_buy_price'] = new_avg_price
    else:  # Selling (negative amount)
        portfolio[crypto]['amount'] += amount  # Add negative amount
        
        # If amount becomes negative, reset to 0
        if portfolio[crypto]['amount'] < 0:
            portfolio[crypto]['amount'] = 0
            
        # If amount becomes 0, reset avg_buy_price to 0
        if portfolio[crypto]['amount'] == 0:
            portfolio[crypto]['avg_buy_price'] = 0.0
    
    return portfolio

def get_historical_portfolio_value(portfolio, days=30):
    """
    Calculate historical portfolio value over time.
    
    Args:
        portfolio (dict): Portfolio data.
        days (int): Number of days to look back.
        
    Returns:
        pd.DataFrame: Historical portfolio values.
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    historical_values = pd.DataFrame(index=pd.date_range(start=start_date, end=end_date))
    
    for crypto, data in portfolio.items():
        crypto_id = crypto.lower()
        if crypto_id == 'xrp':
            crypto_id = 'ripple'
            
        amount = data['amount']
        
        if amount > 0:
            # Get historical price data
            crypto_data = get_crypto_data(crypto_id, start_date, end_date)
            
            if not crypto_data.empty:
                # Calculate value at each point in time
                crypto_value = crypto_data['price'] * amount
                historical_values[crypto] = crypto_value
    
    # Fill NaN values with previous values (or 0 if no previous)
    historical_values = historical_values.fillna(method='ffill').fillna(0)
    
    # Calculate total portfolio value
    historical_values['total'] = historical_values.sum(axis=1)
    
    return historical_values
