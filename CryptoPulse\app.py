import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os

from crypto_data import get_crypto_data, get_current_prices
from utils import format_currency, load_image, get_crypto_icon
from themes import add_theme_selector

# Page configuration
st.set_page_config(
    page_title="CryptoTracker - Analysis & Portfolio Management",
    page_icon="📈",
    layout="wide",
)

# Initialize session state variables if they don't exist
if 'portfolio' not in st.session_state:
    st.session_state.portfolio = {
        'Bitcoin': {'amount': 0.0, 'avg_buy_price': 0.0},
        'Ethereum': {'amount': 0.0, 'avg_buy_price': 0.0},
        'XRP': {'amount': 0.0, 'avg_buy_price': 0.0},
        'Tether': {'amount': 0.0, 'avg_buy_price': 0.0}
    }

if 'alerts' not in st.session_state:
    st.session_state.alerts = []

# Sidebar navigation
with st.sidebar:
    st.title("📊 CryptoTracker")
    st.image("https://images.unsplash.com/photo-1639754390580-2e7437267698", use_container_width=True)
    
    st.subheader("Navigation")
    selected_page = st.radio(
        "Go to:",
        ["Home", "Market Overview", "Portfolio Dashboard", "Price Alerts", "AI Insights", "Advanced Analysis", "Risk Assessment"]
    )
    
    st.subheader("Supported Cryptocurrencies")
    st.markdown("- Bitcoin (BTC)")
    st.markdown("- Ethereum (ETH)")
    st.markdown("- XRP")
    st.markdown("- Tether (USDT)")
    
    st.markdown("---")
    st.caption("Data provided by CoinGecko API")
    
    # Add theme selector
    add_theme_selector()

# Display selected page content
if selected_page == "Home":
    st.title("Welcome to CryptoTracker")
    st.subheader("Your Ultimate Cryptocurrency Analysis & Portfolio Management Platform")
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.image("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5", use_container_width=True)
        st.markdown("""
        ### 📈 Real-time Cryptocurrency Tracking
        Stay updated with real-time price data for Bitcoin, Ethereum, XRP, and Tether.
        """)
        
        st.markdown("""
        ### 📊 Advanced Portfolio Management
        Track your investments, analyze performance, and visualize your asset allocation.
        """)
    
    with col2:
        st.image("https://images.unsplash.com/photo-1556155092-490a1ba16284", use_container_width=True)
        st.markdown("""
        ### 📉 Historical Charts & Trend Analysis
        Analyze historical price data and identify market trends.
        """)
        
        st.markdown("""
        ### 🤖 AI-Powered Insights
        Get intelligent predictions and recommendations based on market data.
        """)
    
    # Current prices overview
    st.header("Current Market Overview")
    
    try:
        current_prices = get_current_prices(['bitcoin', 'ethereum', 'ripple', 'tether'])
        
        if current_prices:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                price_change = current_prices['bitcoin']['price_change_24h']
                color = 'green' if price_change >= 0 else 'red'
                st.metric(
                    "Bitcoin (BTC)", 
                    f"${current_prices['bitcoin']['current_price']:,.2f}",
                    f"{price_change:.2f}%"
                )
            
            with col2:
                price_change = current_prices['ethereum']['price_change_24h']
                color = 'green' if price_change >= 0 else 'red'
                st.metric(
                    "Ethereum (ETH)", 
                    f"${current_prices['ethereum']['current_price']:,.2f}",
                    f"{price_change:.2f}%"
                )
            
            with col3:
                price_change = current_prices['ripple']['price_change_24h']
                color = 'green' if price_change >= 0 else 'red'
                st.metric(
                    "XRP", 
                    f"${current_prices['ripple']['current_price']:,.4f}",
                    f"{price_change:.2f}%"
                )
            
            with col4:
                price_change = current_prices['tether']['price_change_24h']
                color = 'green' if price_change >= 0 else 'red'
                st.metric(
                    "Tether (USDT)", 
                    f"${current_prices['tether']['current_price']:,.2f}",
                    f"{price_change:.2f}%"
                )
        else:
            st.warning("Unable to fetch current price data. Please try again later.")
            
    except Exception as e:
        st.error(f"Error retrieving market data: {str(e)}")
    
    # Featured chart
    st.header("Featured Chart: Bitcoin 30-Day Performance")
    
    try:
        # Get Bitcoin data for last 30 days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        btc_data = get_crypto_data('bitcoin', start_date, end_date)
        
        if not btc_data.empty:
            fig = go.Figure()
            
            fig.add_trace(
                go.Scatter(
                    x=btc_data.index,
                    y=btc_data['price'],
                    mode='lines',
                    name='Price',
                    line=dict(color='#0083B8', width=2)
                )
            )
            
            fig.update_layout(
                title='Bitcoin Price Last 30 Days',
                xaxis_title='Date',
                yaxis_title='Price (USD)',
                template='plotly_white',
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No Bitcoin price data available for the selected time period.")
            
    except Exception as e:
        st.error(f"Error displaying Bitcoin chart: {str(e)}")
        
elif selected_page == "Market Overview":
    import pages.market_overview
    
elif selected_page == "Portfolio Dashboard":
    import pages.portfolio_dashboard
    
elif selected_page == "Price Alerts":
    import pages.price_alerts
    
elif selected_page == "AI Insights":
    import pages.ai_insights
    
elif selected_page == "Advanced Analysis":
    import pages.advanced_analysis
    
elif selected_page == "Risk Assessment":
    import pages.risk_assessment

# Footer
st.markdown("---")
st.caption("© 2023 CryptoTracker | Data provided by CoinGecko API")
